{"version": 3, "names": ["_arrayLikeToArray", "require", "_arrayWithoutHoles", "arr", "Array", "isArray", "arrayLikeToArray"], "sources": ["../../src/helpers/arrayWithoutHoles.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport arrayLikeToArray from \"./arrayLikeToArray.ts\";\n\nexport default function _arrayWithoutHoles<T>(arr: Array<T>) {\n  if (Array.isArray(arr)) return arrayLikeToArray<T>(arr);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,iBAAA,GAAAC,OAAA;AAEe,SAASC,kBAAkBA,CAAIC,GAAa,EAAE;EAC3D,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE,OAAO,IAAAG,yBAAgB,EAAIH,GAAG,CAAC;AACzD", "ignoreList": []}
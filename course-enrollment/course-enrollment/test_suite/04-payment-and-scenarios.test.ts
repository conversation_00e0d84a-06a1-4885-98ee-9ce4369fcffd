/**
 * Payment and Complex Scenario Tests
 * Tests P1-P7 (payments), W1 (waitlist promotion), EE1-EE3 (end-to-end)
 */

import {
  apiClient,
  createHeaders,
  TEST_USERS,
  createTermEnrollmentOpen, createStudentEnrolledCourse,
  createCourseWithWaitlist, validateErrorEnvelope,
  getSuccessData,
  handleApiResponse,
  ApiErrorId, calculateCourseCost, EnrollmentState,
  TermState,
  CourseState,
  UserRole,
  DeliveryMode
} from './helpers';
import { v4 as uuidv4 } from 'uuid';

describe('Payment and Complex Scenario Tests', () => {
  describe('P1: Payment - Student Pays Balance Successfully', () => {
    it('should reduce balance when student makes a payment towards their tuition', async () => {
      // Setup: Student enrolled in course (owes tuition)
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      
      const expectedCost = calculateCourseCost(course.credits);
      const paymentAmount = 10000; // $100
      const expectedNewBalance = expectedCost - paymentAmount;
      
      // Action: Student makes payment
      const headers = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const response = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_A.id,
        { amount: paymentAmount },
        headers
      );
      
      // Assertions
      const paymentResult = handleApiResponse(response, 200);
      expect(paymentResult.student_id).toBe(TEST_USERS.STUDENT_A.id);
      expect(paymentResult.term_id).toBe(term.id);
      expect(paymentResult.new_balance).toBe(expectedNewBalance);
    });
  });

  describe('P2: Payment - Registrar Pays on Behalf of Student', () => {
    it('should allow registrar to record payment for any student', async () => {
      // Setup: Student enrolled in course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      
      const fullAmount = calculateCourseCost(course.credits);
      
      // Action: Registrar makes payment for student
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_A.id,
        { amount: fullAmount },
        headers
      );
      
      // Assertions
      const paymentResult = handleApiResponse(response, 200);
      expect(paymentResult.student_id).toBe(TEST_USERS.STUDENT_A.id);
      expect(paymentResult.new_balance).toBe(0);
    });
  });

  describe('P3: Payment - Unauthorized Role', () => {
    it('should reject payment attempts by professors', async () => {
      // Setup: Student enrolled in course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      
      // Action: Professor attempts payment
      const headers = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const response = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_A.id,
        { amount: 5000 },
        headers
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_UNAUTHORIZED_ROLE });
    });
  });

  describe('P4: Payment - Student Pays for Another Student', () => {
    it('should forbid students from paying another student\'s tuition', async () => {
      // Setup: Two students enrolled in courses
      const { term } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      await createStudentEnrolledCourse(TEST_USERS.STUDENT_B.id);
      
      // Action: Student A tries to pay for Student B
      const headers = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const response = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_B.id,
        { amount: 5000 },
        headers
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_FORBIDDEN });
    });
  });

  describe('P5: Payment - Invalid Amount', () => {
    it('should reject payment with zero or negative amount', async () => {
      // Setup: Student enrolled in course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      
      // Action: Student attempts payment with invalid amount
      const headers = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const response = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_A.id,
        { amount: 0 },
        headers
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_PAYMENT_AMOUNT });
    });
  });

  describe('P6: Payment - Overpayment Not Allowed', () => {
    it('should reject payment exceeding outstanding balance', async () => {
      // Setup: Student enrolled in course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      
      const owedAmount = calculateCourseCost(course.credits);
      const overpaymentAmount = owedAmount + 10000; // $100 more than owed
      
      // Action: Student attempts overpayment
      const headers = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const response = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_A.id,
        { amount: overpaymentAmount },
        headers
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_OVERPAY_NOT_ALLOWED });
    });
  });

  describe('P7: Payment - Student Not Found', () => {
    it('should return error when registrar tries to pay for invalid student', async () => {
      // Setup: Term exists
      const { term } = await createTermEnrollmentOpen();

      // Action: Registrar attempts payment for non-existent student
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.makePayment(
        term.id,
        uuidv4(), // Valid UUID that doesn't exist
        { amount: 5000 },
        headers
      );

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_STUDENT_NOT_FOUND });
    });
  });

  describe('P8: Payment - Decimal Amount Rejected', () => {
    it('should reject payment with non-integer amount', async () => {
      // Setup: Student enrolled in course
      const { term } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);

      // Action: Student attempts payment with decimal amount
      const headers = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const response = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_A.id,
        { amount: 100.5 }, // Non-integer amount
        headers
      );

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_PAYMENT_AMOUNT });
    });
  });

  describe('P9: Payment - Term Not Found', () => {
    it('should return error when payment references nonexistent term', async () => {
      // Setup: Student exists but term doesn't
      const headers = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      // Action: Student attempts payment with invalid term ID
      const response = await apiClient.makePayment(
        uuidv4(), // Valid UUID that doesn't exist
        TEST_USERS.STUDENT_A.id,
        { amount: 5000 },
        headers
      );

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_TERM_NOT_FOUND });
    });
  });

  describe('P10: Payment - Target User Must Be Student', () => {
    it('should reject payment when target user is not a student', async () => {
      // Setup: Term exists
      const { term } = await createTermEnrollmentOpen();

      // Action: Registrar attempts payment for a professor
      const headers = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const response = await apiClient.makePayment(
        term.id,
        TEST_USERS.PROFESSOR_A.id, // Professor ID instead of student
        { amount: 5000 },
        headers
      );

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_STUDENT_NOT_FOUND });
    });
  });

  describe('P11: Payment - Ledger Initialization', () => {
    it('should initialize ledger for student with no prior payment history', async () => {
      // Setup: Create term, course, and enrollment to generate tuition debt
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      const uniqueStudentId = uuidv4();

      // Register the new student by making an API call (this creates the user)
      const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
      await apiClient.getTerm(term.id, studentHeaders);

      // Enroll the new student to create tuition debt
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const enrollResponse = await apiClient.createEnrollment(
        term.id,
        course.id,
        { student_id: uniqueStudentId },
        registrarHeaders
      );
      getSuccessData(enrollResponse);

      // Action: Registrar makes payment for student with no prior payment history
      const response = await apiClient.makePayment(
        term.id,
        uniqueStudentId,
        { amount: 1000 }, // Partial payment to test ledger auto-initialization
        registrarHeaders
      );

      // Assertions: Should succeed and show reduced balance
      const paymentResult = getSuccessData(response);
      expect(paymentResult.new_balance).toBeGreaterThan(0); // Still owes money after partial payment
      expect(paymentResult.new_balance).toBeLessThan(course.credits * 10000); // Less than full tuition
    });
  });

  describe('W1: Waitlist Promotion Chain Reaction', () => {
    it('should automatically enroll waitlisted student when seat becomes available', async () => {
      // Setup: Course with full capacity and waitlist
      const fixtureData = await createCourseWithWaitlist();
      const { term, course, enrollments } = fixtureData;
      
      const enrolledStudent = enrollments.studentA;
      const waitlistedStudent = enrollments.studentC;
      
      // Verify initial state
      expect(enrolledStudent.state).toBe(EnrollmentState.ENROLLED);
      expect(waitlistedStudent.state).toBe(EnrollmentState.WAITLISTED);
      
      // Action: Enrolled student drops the course
      const dropHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const dropResponse = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrolledStudent.id,
        { revision: enrolledStudent.revision },
        dropHeaders
      );
      
      // Verify drop succeeded
      const droppedEnrollment = handleApiResponse(dropResponse, 200);
      expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);
      
      // Wait for promotion to occur (may need small delay for async processing)
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Verify waitlisted student was promoted
      const checkHeaders = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);
      const checkResponse = await apiClient.getEnrollment(
        term.id,
        course.id,
        waitlistedStudent.id,
        checkHeaders
      );
      
      const promotedEnrollment = handleApiResponse(checkResponse, 200);
      expect(promotedEnrollment.state).toBe(EnrollmentState.ENROLLED);
      
      // Verify course still shows full capacity (seat freed was immediately taken)
      const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const courseResponse = await apiClient.getCourse(term.id, course.id, regHeaders);
      const updatedCourse = handleApiResponse(courseResponse, 200);
      
      expect(updatedCourse.available_seats).toBe(0);
      expect(updatedCourse.enrolled_count).toBe(course.capacity);
      expect(updatedCourse.waitlist_count).toBe(0);
    });
  });

  describe('EE1: End-to-End Student Enrollment to Course Completion and Payment', () => {
    it('should complete full student lifecycle from enrollment to payment', async () => {
      // Step 1: Registrar creates a term
      const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const termResponse = await apiClient.createTerm({ name: 'EE1 Test Term' }, regHeaders);
      const term = getSuccessData(termResponse);
      expect(term.state).toBe(TermState.PLANNING);
      
      // Step 2: Registrar opens the term
      const openResponse = await apiClient.openTermRegistration(
        term.id,
        { revision: term.revision },
        regHeaders
      );
      const openTerm = getSuccessData(openResponse);
      expect(openTerm.state).toBe(TermState.ENROLLMENT_OPEN);
      
      // Step 3: Professor creates a course
      const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const courseResponse = await apiClient.createCourse(
        openTerm.id,
        {
          code: 'EE101',
          title: 'End-to-End Course',
          credits: 3,
          capacity: 20,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: 'Lecture Hall A'
        },
        profHeaders
      );
      const course = getSuccessData(courseResponse);
      expect(course.state).toBe(CourseState.DRAFT);
      
      // Step 4: Professor publishes the course
      const publishResponse = await apiClient.publishCourse(
        openTerm.id,
        course.id,
        { revision: course.revision },
        profHeaders
      );
      const publishedCourse = getSuccessData(publishResponse);
      expect(publishedCourse.state).toBe(CourseState.OPEN);
      
      // Step 5: Student enrolls in the course
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const enrollResponse = await apiClient.createEnrollment(
        openTerm.id,
        publishedCourse.id,
        {},
        studentHeaders
      );
      const enrollment = getSuccessData(enrollResponse);
      expect(enrollment.state).toBe(EnrollmentState.ENROLLED);
      
      // Step 6: Registrar closes term registration
      const closeResponse = await apiClient.closeTermRegistration(
        openTerm.id,
        { revision: openTerm.revision },
        regHeaders
      );
      const closedTerm = getSuccessData(closeResponse);
      expect(closedTerm.state).toBe(TermState.ENROLLMENT_CLOSED);
      
      // Verify course moved to IN_PROGRESS
      const courseCheckResponse = await apiClient.getCourse(openTerm.id, publishedCourse.id, regHeaders);
      const inProgressCourse = getSuccessData(courseCheckResponse);
      expect(inProgressCourse.state).toBe(CourseState.IN_PROGRESS);
      
      // Step 7: Registrar concludes the term
      const concludeResponse = await apiClient.concludeTerm(
        closedTerm.id,
        { revision: closedTerm.revision },
        regHeaders
      );
      const concludedTerm = getSuccessData(concludeResponse);
      expect(concludedTerm.state).toBe(TermState.CONCLUDED);
      
      // Verify course and enrollment completed
      const finalCourseResponse = await apiClient.getCourse(concludedTerm.id, publishedCourse.id, regHeaders);
      const completedCourse = getSuccessData(finalCourseResponse);
      expect(completedCourse.state).toBe(CourseState.COMPLETED);
      
      const finalEnrollResponse = await apiClient.getEnrollment(
        concludedTerm.id,
        publishedCourse.id,
        enrollment.id,
        regHeaders
      );
      const completedEnrollment = getSuccessData(finalEnrollResponse);
      expect(completedEnrollment.state).toBe(EnrollmentState.COMPLETED);
      
      // Step 8: Student pays outstanding tuition
      const expectedCost = calculateCourseCost(publishedCourse.credits);
      const paymentResponse = await apiClient.makePayment(
        concludedTerm.id,
        TEST_USERS.STUDENT_A.id,
        { amount: expectedCost },
        studentHeaders
      );
      const paymentResult = getSuccessData(paymentResponse);
      expect(paymentResult.new_balance).toBe(0);
    });
  });

  describe('EE2: End-to-End Professor Course Creation to Cancellation', () => {
    it('should handle complete course lifecycle from creation to cancellation with enrollments', async () => {
      // Step 1: Setup term and course
      const { term } = await createTermEnrollmentOpen('EE2 Test Term');
      
      const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const courseResponse = await apiClient.createCourse(
        term.id,
        {
          code: 'EE201',
          title: 'Professor Course Test',
          credits: 4,
          capacity: 2,
          delivery_mode: DeliveryMode.ONLINE,
          online_link: 'https://example.com/ee201'
        },
        profHeaders
      );
      const course = getSuccessData(courseResponse);
      
      // Step 2: Professor publishes course
      const publishResponse = await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        profHeaders
      );
      const publishedCourse = getSuccessData(publishResponse);
      expect(publishedCourse.state).toBe(CourseState.OPEN);
      
      // Step 3: Students enroll (fill capacity + waitlist)
      const student1Headers = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const student2Headers = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);
      const student3Headers = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);
      
      const enroll1Response = await apiClient.createEnrollment(term.id, publishedCourse.id, {}, student1Headers);
      const enrollment1 = getSuccessData(enroll1Response);
      expect(enrollment1.state).toBe(EnrollmentState.ENROLLED);
      
      const enroll2Response = await apiClient.createEnrollment(term.id, publishedCourse.id, {}, student2Headers);
      const enrollment2 = getSuccessData(enroll2Response);
      expect(enrollment2.state).toBe(EnrollmentState.ENROLLED);
      
      const enroll3Response = await apiClient.createEnrollment(term.id, publishedCourse.id, {}, student3Headers);
      const enrollment3 = getSuccessData(enroll3Response);
      expect(enrollment3.state).toBe(EnrollmentState.WAITLISTED);
      
      // Step 4: Professor cancels the course
      const cancelResponse = await apiClient.cancelCourse(
        term.id,
        publishedCourse.id,
        { revision: publishedCourse.revision },
        profHeaders
      );
      const cancelledCourse = getSuccessData(cancelResponse);
      expect(cancelledCourse.state).toBe(CourseState.CANCELLED);
      
      // Step 5: Verify all enrollments are dropped
      const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      const enrollment1Check = await apiClient.getEnrollment(term.id, publishedCourse.id, enrollment1.id, regHeaders);
      const droppedEnrollment1 = getSuccessData(enrollment1Check);
      expect(droppedEnrollment1.state).toBe(EnrollmentState.DROPPED);
      
      const enrollment2Check = await apiClient.getEnrollment(term.id, publishedCourse.id, enrollment2.id, regHeaders);
      const droppedEnrollment2 = getSuccessData(enrollment2Check);
      expect(droppedEnrollment2.state).toBe(EnrollmentState.DROPPED);
      
      const enrollment3Check = await apiClient.getEnrollment(term.id, publishedCourse.id, enrollment3.id, regHeaders);
      const droppedEnrollment3 = getSuccessData(enrollment3Check);
      expect(droppedEnrollment3.state).toBe(EnrollmentState.DROPPED);
      
      // Step 6: Verify course shows all seats available
      const finalCourseResponse = await apiClient.getCourse(term.id, publishedCourse.id, regHeaders);
      const finalCourse = getSuccessData(finalCourseResponse);
      expect(finalCourse.available_seats).toBe(finalCourse.capacity);
    });
  });

  describe('EE3: End-to-End Waitlist Promotion and Drop Penalty Dynamics', () => {
    it('should handle complex interactions with waitlist, drops, and penalties', async () => {
      // Step 1: Setup term and course with capacity 2
      const { term } = await createTermEnrollmentOpen('EE3 Test Term');
      
      const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const courseResponse = await apiClient.createCourse(
        term.id,
        {
          code: 'EE301',
          title: 'Complex Scenarios Course',
          credits: 3,
          capacity: 2,
          delivery_mode: DeliveryMode.HYBRID,
          location: 'Room 301',
          online_link: 'https://example.com/ee301'
        },
        profHeaders
      );
      const course = getSuccessData(courseResponse);
      
      const publishResponse = await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        profHeaders
      );
      const publishedCourse = getSuccessData(publishResponse);
      
      // Step 2: Fill course and create waitlist
      const studentAHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const studentBHeaders = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);
      const studentCHeaders = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);
      const studentDHeaders = createHeaders(TEST_USERS.STUDENT_D.id, UserRole.STUDENT);
      
      // Students A and B enroll (fill capacity)
      const enrollAResponse = await apiClient.createEnrollment(term.id, publishedCourse.id, {}, studentAHeaders);
      const enrollmentA = getSuccessData(enrollAResponse);
      
      const enrollBResponse = await apiClient.createEnrollment(term.id, publishedCourse.id, {}, studentBHeaders);
      const enrollmentB = getSuccessData(enrollBResponse);
      
      // Students C and D go to waitlist
      const enrollCResponse = await apiClient.createEnrollment(term.id, publishedCourse.id, {}, studentCHeaders);
      const enrollmentC = getSuccessData(enrollCResponse);
      expect(enrollmentC.state).toBe(EnrollmentState.WAITLISTED);
      
      const enrollDResponse = await apiClient.createEnrollment(term.id, publishedCourse.id, {}, studentDHeaders);
      const enrollmentD = getSuccessData(enrollDResponse);
      expect(enrollmentD.state).toBe(EnrollmentState.WAITLISTED);
      
      // Step 3: Student A drops (first drop for A) - should promote Student C
      const dropAResponse = await apiClient.dropEnrollment(
        term.id,
        publishedCourse.id,
        enrollmentA.id,
        { revision: enrollmentA.revision },
        studentAHeaders
      );
      const droppedA = getSuccessData(dropAResponse);
      expect(droppedA.state).toBe(EnrollmentState.DROPPED);
      
      // Allow time for promotion
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Verify Student C was promoted
      const checkCResponse = await apiClient.getEnrollment(term.id, publishedCourse.id, enrollmentC.id, studentCHeaders);
      const promotedC = getSuccessData(checkCResponse);
      expect(promotedC.state).toBe(EnrollmentState.ENROLLED);
      
      // Step 4: Student B drops (first drop for B) - should promote Student D
      const dropBResponse = await apiClient.dropEnrollment(
        term.id,
        publishedCourse.id,
        enrollmentB.id,
        { revision: enrollmentB.revision },
        studentBHeaders
      );
      const droppedB = getSuccessData(dropBResponse);
      expect(droppedB.state).toBe(EnrollmentState.DROPPED);
      
      // Allow time for promotion
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Verify Student D was promoted
      const checkDResponse = await apiClient.getEnrollment(term.id, publishedCourse.id, enrollmentD.id, studentDHeaders);
      const promotedD = getSuccessData(checkDResponse);
      expect(promotedD.state).toBe(EnrollmentState.ENROLLED);
      
      // Step 5: Students continue dropping to reach penalty threshold
      // Create additional courses for students to enroll and drop
      const course2Response = await apiClient.createCourse(
        term.id,
        {
          code: 'EE302',
          title: 'Drop Test Course 2',
          credits: 3,
          capacity: 5,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: 'Room 302'
        },
        profHeaders
      );
      const course2 = getSuccessData(course2Response);
      
      const publishCourse2Response = await apiClient.publishCourse(
        term.id,
        course2.id,
        { revision: course2.revision },
        profHeaders
      );
      
      // Student A enrolls and drops from course2 (2nd drop)
      const enrollA2Response = await apiClient.createEnrollment(term.id, course2.id, {}, studentAHeaders);
      const enrollmentA2 = getSuccessData(enrollA2Response);
      
      const dropA2Response = await apiClient.dropEnrollment(
        term.id,
        course2.id,
        enrollmentA2.id,
        { revision: enrollmentA2.revision },
        studentAHeaders
      );
      
      // Create course3 for 3rd drop (should trigger penalty)
      const course3Response = await apiClient.createCourse(
        term.id,
        {
          code: 'EE303',
          title: 'Drop Test Course 3',
          credits: 3,
          capacity: 5,
          delivery_mode: DeliveryMode.ONLINE,
          online_link: 'https://example.com/ee303'
        },
        profHeaders
      );
      const course3 = getSuccessData(course3Response);
      
      const publishCourse3Response = await apiClient.publishCourse(
        term.id,
        course3.id,
        { revision: course3.revision },
        profHeaders
      );
      
      // Student A enrolls and drops from course3 (3rd drop - should trigger penalty)
      const enrollA3Response = await apiClient.createEnrollment(term.id, course3.id, {}, studentAHeaders);
      const enrollmentA3 = getSuccessData(enrollA3Response);
      
      const dropA3Response = await apiClient.dropEnrollment(
        term.id,
        course3.id,
        enrollmentA3.id,
        { revision: enrollmentA3.revision },
        studentAHeaders
      );
      const droppedA3 = getSuccessData(dropA3Response);
      expect(droppedA3.state).toBe(EnrollmentState.DROPPED);
      
      // Step 6: Try 4th drop (should be blocked)
      const course4Response = await apiClient.createCourse(
        term.id,
        {
          code: 'EE304',
          title: 'Drop Test Course 4',
          credits: 3,
          capacity: 5,
          delivery_mode: DeliveryMode.HYBRID,
          location: 'Room 304',
          online_link: 'https://example.com/ee304'
        },
        profHeaders
      );
      const course4 = getSuccessData(course4Response);
      
      const publishCourse4Response = await apiClient.publishCourse(
        term.id,
        course4.id,
        { revision: course4.revision },
        profHeaders
      );
      
      const enrollA4Response = await apiClient.createEnrollment(term.id, course4.id, {}, studentAHeaders);
      const enrollmentA4 = getSuccessData(enrollA4Response);
      
      // 4th drop attempt should be blocked
      const drop4AttemptResponse = await apiClient.dropEnrollment(
        term.id,
        course4.id,
        enrollmentA4.id,
        { revision: enrollmentA4.revision },
        studentAHeaders
      );
      
      validateErrorEnvelope(drop4AttemptResponse, { expectedErrorId: ApiErrorId.ERR_TOO_MANY_DROPS });
    });
  });
});
/**
 * Advanced and Edge Case Test Scenarios
 * 
 * These tests focus on boundary conditions, race conditions, error precedence,
 * RBAC edge cases, and complex state interactions for the Course Enrollment API.
 */

import {
  apiClient,
  createHeaders,
  TEST_USERS,
  createTermPlanning,
  createTermEnrollmentOpen,
  createTermConcluded,
  createCourseDraft,
  DeliveryMode,
  createCourseOpen,
  createCourseFullCapacity,
  createCourseWithWaitlist,
  createStudentMaxCreditLoad,
  validateErrorEnvelope,
  getSuccessData,
  UserRole,
  TermState,
  CourseState,
  EnrollmentState,
  ApiErrorId
} from './helpers';

describe('Advanced and Edge Case Tests', () => {

  describe('Focus Area 1: Boundary Condition Verification', () => {

    describe('TC-ADV-01: Student reaches exactly 18 credits maximum', () => {
      it('should allow enrollment when student hits exactly 18 credits without error', async () => {
        const { term } = await createTermEnrollmentOpen();
        const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
        const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

        // Ensure Professor user is registered in system for later Registrar course creations
        await apiClient.getTerm(term.id, createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR));

        // Create four courses with 5, 4, 4, 4 credits (total 17)
        const courseCredits = [5, 4, 4, 4];
        const enrollments = [];

        for (let i = 0; i < courseCredits.length; i++) {
          // Create course
          const createResp = await apiClient.createCourse(
            term.id,
            {
              code: `MIN00${i}`,
              title: `Mini Course ${i}`,
              credits: courseCredits[i],
              capacity: 5,
              professor_id: TEST_USERS.PROFESSOR_A.id,
              delivery_mode: DeliveryMode.IN_PERSON,
              location: `Room ${200 + i}`
            },
            regHeaders
          );
          const course = getSuccessData(createResp);

          // Publish course
          await apiClient.publishCourse(
            term.id,
            course.id,
            { revision: course.revision },
            regHeaders
          );

          // Enroll StudentA
          const enrollResp = await apiClient.createEnrollment(
            term.id,
            course.id,
            {},
            studentHeaders
          );
          enrollments.push(getSuccessData(enrollResp));
        }

        // Create a 1-credit course to reach exactly 18 credits
        const cap18Resp = await apiClient.createCourse(
          term.id,
          {
            code: 'CAP018',
            title: 'Capstone Credit',
            credits: 1,
            capacity: 5,
            professor_id: TEST_USERS.PROFESSOR_A.id,
            delivery_mode: DeliveryMode.IN_PERSON,
            location: 'Room 300'
          },
          regHeaders
        );
        const cap18Course = getSuccessData(cap18Resp);

        // Publish CAP18
        await apiClient.publishCourse(
          term.id,
          cap18Course.id,
          { revision: cap18Course.revision },
          regHeaders
        );

        // StudentA enrolls in CAP18, hitting exactly 18 credits
        const finalEnrollResp = await apiClient.createEnrollment(
          term.id,
          cap18Course.id,
          {},
          studentHeaders
        );

        expect(finalEnrollResp.status).toBe(201);
        const finalEnrollment = getSuccessData(finalEnrollResp);
        expect(finalEnrollment.state).toBe(EnrollmentState.ENROLLED);

        // Verify total credits = 18
        const totalCredits = courseCredits.reduce((sum, credits) => sum + credits, 0) + 1;
        expect(totalCredits).toBe(18);
      });
    });

    describe('TC-ADV-02: Professor creates exactly 5 courses (maximum)', () => {
      it('should allow professor to create their 5th course without triggering MAX_COURSES_REACHED', async () => {
        const { term } = await createTermPlanning();
        const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

        // Create 4 courses first
        for (let i = 0; i < 4; i++) {
          const response = await apiClient.createCourse(
            term.id,
            {
              code: `PQ${i + 1}00`,
              title: `ProfQ Course ${i + 1}`,
              credits: 3,
              capacity: 10,
              delivery_mode: DeliveryMode.IN_PERSON,
              location: `Room ${400 + i}`
            },
            profHeaders
          );
          expect(response.status).toBe(201);
        }

        // Create 5th course (should succeed)
        const fifthCourseResp = await apiClient.createCourse(
          term.id,
          {
            code: 'PQ500',
            title: 'ProfQ Course 5',
            credits: 3,
            capacity: 10,
            delivery_mode: DeliveryMode.IN_PERSON,
            location: 'Room 405'
          },
          profHeaders
        );

        expect(fifthCourseResp.status).toBe(201);
        const fifthCourse = getSuccessData(fifthCourseResp);
        expect(fifthCourse.state).toBe(CourseState.DRAFT);
      });
    });

  });

  describe('Focus Area 2: Rule Collision & Error Precedence', () => {

    describe('TC-ADV-03A: Professor enrollment blocked by role check', () => {
      it('should return ERR_UNAUTHORIZED_ROLE before deeper validation', async () => {
        const { term, course } = await createCourseDraft();
        const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

        const response = await apiClient.createEnrollment(
          term.id,
          course.id,
          {},
          profHeaders
        );

        expect(response.status).toBe(403);
        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_UNAUTHORIZED_ROLE });
      });
    });

    describe('TC-ADV-03B: Student enrollment in concluded term', () => {
      it('should return ERR_REGISTRATION_CLOSED for concluded term', async () => {
        // 1. Create an OPEN course
        const { term, course } = await createCourseOpen();

        // 2. Close registration and then conclude the term so the course now belongs to a CONCLUDED term
        const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
        const closedResp = await apiClient.closeTermRegistration(
          term.id,
          { revision: term.revision },
          regHeaders
        );
        const closedTerm = getSuccessData(closedResp);

        await apiClient.concludeTerm(
          closedTerm.id,
          { revision: closedTerm.revision },
          regHeaders
        );

        const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

        const response = await apiClient.createEnrollment(
          closedTerm.id,
          course.id,
          {},
          studentHeaders
        );

        expect(response.status).toBe(409);
        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE });
      });
    });

    describe('TC-ADV-04: Credit limit overrides waitlist behavior', () => {
      it('should return ERR_CREDIT_LIMIT_EXCEEDED instead of waitlisting when student at 18 credits', async () => {
        // 1. Set up StudentA at exactly 18 enrolled credits **within a single term**
        const { term: creditMaxTerm } = await createStudentMaxCreditLoad();

        // 2. Inside the SAME term, create an additional 3-credit course with capacity 2
        const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
        const createResp = await apiClient.createCourse(
          creditMaxTerm.id,
          {
            code: 'FCAP001',
            title: 'Full Capacity Course',
            credits: 3,
            capacity: 2,
            professor_id: TEST_USERS.PROFESSOR_A.id,
            delivery_mode: DeliveryMode.IN_PERSON,
            location: 'Room 500'
          },
          regHeaders
        );
        const newCourse = getSuccessData(createResp);

        // Publish the course so it is OPEN for enrollment
        await apiClient.publishCourse(
          creditMaxTerm.id,
          newCourse.id,
          { revision: newCourse.revision },
          regHeaders
        );

        // 3. Fill the course to capacity with StudentB and StudentC
        const studentBHeaders = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);
        const studentCHeaders = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);
        await apiClient.createEnrollment(creditMaxTerm.id, newCourse.id, {}, studentBHeaders);
        await apiClient.createEnrollment(creditMaxTerm.id, newCourse.id, {}, studentCHeaders);

        // Sanity check: registrar fetch to ensure course is full (available_seats = 0)
        const regCheckHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
        await apiClient.getCourse(creditMaxTerm.id, newCourse.id, regCheckHeaders);

        // 4. StudentA (already at 18 credits in the SAME term) attempts to enroll
        const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

        const response = await apiClient.createEnrollment(
          creditMaxTerm.id,
          newCourse.id,
          {},
          studentHeaders
        );

        expect(response.status).toBe(409);
        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_CREDIT_LIMIT_EXCEEDED });
      });
    });

  });

  describe('Focus Area 3: Logical State Interference (Multi-Actor)', () => {

    describe('TC-ADV-05: Optimistic locking protection with concurrent registrars', () => {
      it('should fail Registrar B with stale revision after Registrar A updates term', async () => {
        const { term } = await createTermEnrollmentOpen();
        const regAHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
        const regBHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

        // Both registrars have the same initial revision
        const initialRevision = term.revision;

        // Registrar A closes registration
        const closeResp = await apiClient.closeTermRegistration(
          term.id,
          { revision: initialRevision },
          regAHeaders
        );
        expect(closeResp.status).toBe(200);
        const updatedTerm = getSuccessData(closeResp);
        expect(updatedTerm.state).toBe(TermState.ENROLLMENT_CLOSED);

        // Registrar B immediately attempts to conclude term with stale revision
        const concludeResp = await apiClient.concludeTerm(
          term.id,
          { revision: initialRevision }, // stale revision
          regBHeaders
        );

        expect(concludeResp.status).toBe(409);
        validateErrorEnvelope(concludeResp, { expectedErrorId: ApiErrorId.ERR_REV_CONFLICT });
      });
    });

  });

  describe('Focus Area 4: Adversarial Action Sequencing', () => {

    describe('TC-ADV-06: Auto-promotion followed by immediate drop', () => {
      it('should correctly handle ledger and penalty when promoted student immediately drops', async () => {
        const { term, course, enrollments } = await createCourseWithWaitlist();
        const studentAHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
        const studentCHeaders = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);

        // StudentA drops, freeing a seat and promoting StudentC
        const dropResp = await apiClient.dropEnrollment(
          term.id,
          course.id,
          enrollments.studentA.id,
          { revision: enrollments.studentA.revision },
          studentAHeaders
        );
        expect(dropResp.status).toBe(200);

        // Verify StudentC is now enrolled
        const checkEnrollResp = await apiClient.getEnrollment(
          term.id,
          course.id,
          enrollments.studentC.id,
          studentCHeaders
        );
        const updatedEnrollmentC = getSuccessData(checkEnrollResp);
        expect(updatedEnrollmentC.state).toBe(EnrollmentState.ENROLLED);

        // StudentC immediately drops the course
        const dropCResp = await apiClient.dropEnrollment(
          term.id,
          course.id,
          enrollments.studentC.id,
          { revision: updatedEnrollmentC.revision },
          studentCHeaders
        );
        expect(dropCResp.status).toBe(200);
        const droppedEnrollmentC = getSuccessData(dropCResp);
        expect(droppedEnrollmentC.state).toBe(EnrollmentState.DROPPED);

        // Verify course has available seats again
        const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
        const courseResp = await apiClient.getCourse(term.id, course.id, regHeaders);
        const updatedCourse = getSuccessData(courseResp);
        expect(updatedCourse.available_seats).toBe(1);
      });
    });

    describe('TC-EMG-01: Third drop penalty calculation', () => {
      it('should apply refund before penalty, resulting in positive balance', async () => {
        const { term } = await createTermEnrollmentOpen();
        const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
        const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

        // Create and publish a 3-credit course
        const createResp = await apiClient.createCourse(
          term.id,
          {
            code: 'TST003',
            title: 'Test Course',
            credits: 3,
            capacity: 5,
            professor_id: TEST_USERS.PROFESSOR_A.id,
            delivery_mode: DeliveryMode.IN_PERSON,
            location: 'Room 700'
          },
          regHeaders
        );
        const course = getSuccessData(createResp);

        await apiClient.publishCourse(
          term.id,
          course.id,
          { revision: course.revision },
          regHeaders
        );

        // StudentA enrolls in course (balance +30,000¢)
        const enrollResp = await apiClient.createEnrollment(
          term.id,
          course.id,
          {},
          studentHeaders
        );
        let enrollment = getSuccessData(enrollResp);

        // Perform two enroll→drop cycles
        for (let i = 0; i < 2; i++) {
          // Drop
          const dropResp = await apiClient.dropEnrollment(
            term.id,
            course.id,
            enrollment.id,
            { revision: enrollment.revision },
            studentHeaders
          );
          expect(dropResp.status).toBe(200);

          // Re-enroll
          const reEnrollResp = await apiClient.createEnrollment(
            term.id,
            course.id,
            {},
            studentHeaders
          );
          enrollment = getSuccessData(reEnrollResp);
        }

        // Third drop should trigger penalty
        const finalDropResp = await apiClient.dropEnrollment(
          term.id,
          course.id,
          enrollment.id,
          { revision: enrollment.revision },
          studentHeaders
        );
        expect(finalDropResp.status).toBe(200);

        // The final balance should be DROP_PENALTY_FEE (5000¢)
        // because: +30000 (charge) -30000 (refund) +5000 (penalty) = 5000
      });
    });

    describe('TC-EMG-02: Post-override state normalization', () => {
      it('should block self-enrollment after dropping from registrar override to exactly 18 credits', async () => {
        // Create student at max credit load (18 credits)
        const { term } = await createStudentMaxCreditLoad();
        const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
        const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

        // Register Professor_B for upcoming Registrar-created courses
        await apiClient.getTerm(term.id, createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR));

        // Create and publish a 3-credit course
        const createResp = await apiClient.createCourse(
          term.id,
          {
            code: 'OVR003',
            title: 'Override Course',
            credits: 3,
            capacity: 5,
            professor_id: TEST_USERS.PROFESSOR_A.id,
            delivery_mode: DeliveryMode.IN_PERSON,
            location: 'Room 501'
          },
          regHeaders
        );
        const course = getSuccessData(createResp);

        await apiClient.publishCourse(
          term.id,
          course.id,
          { revision: course.revision },
          regHeaders
        );

        // Registrar force-enrolls StudentA (total = 21 credits)
        const overrideEnrollResp = await apiClient.createEnrollment(
          term.id,
          course.id,
          { student_id: TEST_USERS.STUDENT_A.id },
          regHeaders
        );
        expect(overrideEnrollResp.status).toBe(201);
        const overrideEnrollment = getSuccessData(overrideEnrollResp);

        // StudentA drops the course (back to 18 credits)
        const dropResp = await apiClient.dropEnrollment(
          term.id,
          course.id,
          overrideEnrollment.id,
          { revision: overrideEnrollment.revision },
          studentHeaders
        );
        expect(dropResp.status).toBe(200);

        // Create 1-credit micro-course
        const microResp = await apiClient.createCourse(
          term.id,
          {
            code: 'MIC001',
            title: 'Micro-Credit',
            credits: 1,
            capacity: 5,
            professor_id: TEST_USERS.PROFESSOR_B.id,
            delivery_mode: DeliveryMode.IN_PERSON,
            location: 'Room 502'
          },
          regHeaders
        );
        const microCourse = getSuccessData(microResp);

        await apiClient.publishCourse(
          term.id,
          microCourse.id,
          { revision: microCourse.revision },
          regHeaders
        );

        // StudentA attempts to self-enroll in micro course (would reach 19 credits)
        const selfEnrollResp = await apiClient.createEnrollment(
          term.id,
          microCourse.id,
          {},
          studentHeaders
        );

        expect(selfEnrollResp.status).toBe(409);
        validateErrorEnvelope(selfEnrollResp, { expectedErrorId: ApiErrorId.ERR_CREDIT_LIMIT_EXCEEDED });
      });
    });

    describe('TC-EMG-03: Sequential max-course limit enforcement', () => {
      it('should allow 5th course but reject 6th course immediately', async () => {
        const { term } = await createTermPlanning();
        const profHeaders = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);

        // Create 4 courses
        for (let i = 0; i < 4; i++) {
          const response = await apiClient.createCourse(
            term.id,
            {
              code: `PS${i + 1}00`,
              title: `Course ${i + 1}`,
              credits: 3,
              capacity: 10,
              delivery_mode: DeliveryMode.IN_PERSON,
              location: `Room ${600 + i}`
            },
            profHeaders
          );
          expect(response.status).toBe(201);
        }

        // 5th course should succeed
        const fifthResp = await apiClient.createCourse(
          term.id,
          {
            code: 'PS500',
            title: 'Course 5',
            credits: 3,
            capacity: 10,
            delivery_mode: DeliveryMode.IN_PERSON,
            location: 'Room 605'
          },
          profHeaders
        );
        expect(fifthResp.status).toBe(201);

        // 6th course should fail
        const sixthResp = await apiClient.createCourse(
          term.id,
          {
            code: 'PS600',
            title: 'Course 6',
            credits: 3,
            capacity: 10,
            delivery_mode: DeliveryMode.IN_PERSON,
            location: 'Room 606'
          },
          profHeaders
        );

        expect(sixthResp.status).toBe(409);
        validateErrorEnvelope(sixthResp, { expectedErrorId: ApiErrorId.ERR_MAX_COURSES_REACHED });
      });
    });

    describe('TC-EMG-04: Drop-count immunity on course cancellation', () => {
      it('should not increment drop count for auto-drops from course cancellation', async () => {
        const { term } = await createTermEnrollmentOpen();
        const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
        const studentHeaders = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);
        const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

        // Student self-drops two courses first
        for (let i = 0; i < 2; i++) {
          // Create and publish temp course
          const createResp = await apiClient.createCourse(
            term.id,
            {
              code: `TMP00${i}`,
              title: `Temp Course ${i}`,
              credits: 3,
              capacity: 5,
              professor_id: TEST_USERS.PROFESSOR_A.id,
              delivery_mode: DeliveryMode.IN_PERSON,
              location: `Room ${800 + i}`
            },
            regHeaders
          );
          const tempCourse = getSuccessData(createResp);

          await apiClient.publishCourse(
            term.id,
            tempCourse.id,
            { revision: tempCourse.revision },
            regHeaders
          );

          // Enroll student
          const enrollResp = await apiClient.createEnrollment(
            term.id,
            tempCourse.id,
            {},
            studentHeaders
          );
          const enrollment = getSuccessData(enrollResp);

          // Student drops (self-initiated)
          const dropResp = await apiClient.dropEnrollment(
            term.id,
            tempCourse.id,
            enrollment.id,
            { revision: enrollment.revision },
            studentHeaders
          );
          expect(dropResp.status).toBe(200);
        }

        // Create course that will be cancelled
        const cxlResp = await apiClient.createCourse(
          term.id,
          {
            code: 'CXL001',
            title: 'Cancellable',
            credits: 3,
            capacity: 5,
            professor_id: TEST_USERS.PROFESSOR_A.id,
            delivery_mode: DeliveryMode.IN_PERSON,
            location: 'Room 900'
          },
          regHeaders
        );
        const cxlCourse = getSuccessData(cxlResp);

        const publishCxlResp = await apiClient.publishCourse(
          term.id,
          cxlCourse.id,
          { revision: cxlCourse.revision },
          profHeaders
        );
        const publishedCxlCourse = getSuccessData(publishCxlResp);

        // Enroll StudentB
        const enrollResp = await apiClient.createEnrollment(
          term.id,
          publishedCxlCourse.id,
          {},
          studentHeaders
        );
        expect(enrollResp.status).toBe(201);

        // Professor cancels course (auto-drop StudentB)
        const cancelResp = await apiClient.cancelCourse(
          term.id,
          publishedCxlCourse.id,
          { revision: publishedCxlCourse.revision },
          profHeaders
        );
        expect(cancelResp.status).toBe(200);

        // Drop count should still be 2 (not incremented by course cancellation)
        // This would need additional API endpoints to verify drop count stats
        // For now, we verify the cancellation succeeded without issues
      });
    });

  });

  describe('RBAC Edge Cases', () => {

    describe('TC-GAP-01: Student roster access forbidden', () => {
      it('should return ERR_UNAUTHORIZED_ROLE when student tries to list course enrollments', async () => {
        const { term, course } = await createCourseOpen();
        const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

        const response = await apiClient.listEnrollments(
          term.id,
          course.id,
          {},
          studentHeaders
        );

        expect(response.status).toBe(403);
        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_UNAUTHORIZED_ROLE });
      });
    });

    describe('TC-GAP-02: DRAFT course hidden from unauthorized student', () => {
      it('should return ERR_COURSE_NOT_FOUND for DRAFT course accessed by student', async () => {
        const { term, course } = await createCourseDraft();
        const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

        const response = await apiClient.getCourse(
          term.id,
          course.id,
          studentHeaders
        );

        expect(response.status).toBe(404);
        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_COURSE_NOT_FOUND });
      });
    });

    describe('TC-GAP-03: Malformed UUID handling', () => {
      it('should return ERR_INVALID_ID_FORMAT for invalid UUID', async () => {
        const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

        const response = await apiClient.getTerm('not-a-uuid', regHeaders);

        expect(response.status).toBe(400);
        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
      });
    });

    describe('TC-GAP-04: Cross-term isolation', () => {
      it('should return ERR_COURSE_NOT_FOUND when accessing course through wrong term', async () => {
        const { term: planningTerm } = await createTermPlanning();
        const { course } = await createCourseOpen();
        const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

        // Try to access course from openTerm via planningTerm path
        const response = await apiClient.getCourse(
          planningTerm.id,
          course.id,
          regHeaders
        );

        expect(response.status).toBe(404);
        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_COURSE_NOT_FOUND });
      });
    });

  });

  describe('Capacity and Waitlist Edge Cases', () => {
    describe('TC-GAP-07: Multi-seat promotion cascade', () => {
      it('should promote exactly two waitlisted students when two seats are freed', async () => {
        const { term, course, enrollments } = await createCourseWithWaitlist();
        
        // First, we need to add more students to the waitlist
        const studentDHeaders = createHeaders(TEST_USERS.STUDENT_D.id, UserRole.STUDENT);
        const waitlistResp = await apiClient.createEnrollment(
          term.id,
          course.id,
          {},
          studentDHeaders
        );
        const studentDEnrollment = getSuccessData(waitlistResp);
        expect(studentDEnrollment.state).toBe(EnrollmentState.WAITLISTED);

        // Drop both enrolled students sequentially
        const studentAHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
        const studentBHeaders = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);

        const dropAResp = await apiClient.dropEnrollment(
          term.id,
          course.id,
          enrollments.studentA.id,
          { revision: enrollments.studentA.revision },
          studentAHeaders
        );
        expect(dropAResp.status).toBe(200);

        const dropBResp = await apiClient.dropEnrollment(
          term.id,
          course.id,
          enrollments.studentB.id,
          { revision: enrollments.studentB.revision },
          studentBHeaders
        );
        expect(dropBResp.status).toBe(200);

        // Check final course state
        const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
        const finalCourseResp = await apiClient.getCourse(term.id, course.id, regHeaders);
        const finalCourse = getSuccessData(finalCourseResp);

        // Should still have 2 enrolled (C and D promoted), 0 waitlisted, 0 available
        expect(finalCourse.enrolled_count).toBe(2);
        expect(finalCourse.waitlist_count).toBe(0);
        expect(finalCourse.available_seats).toBe(0);
      });
    });

  });

});
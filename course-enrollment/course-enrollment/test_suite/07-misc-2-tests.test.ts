/**
 * High-Value Functional Tests
 * 
 * These tests exercise critical business logic explicitly grounded in the PRD.
 * They target state machine transitions, multi-invariant interactions, and
 * role-based access control boundaries.
 */

import {
  apiClient,
  createHeaders,
  TEST_USERS,
  createTermEnrollmentOpen,
  createTermPlanning,
  createTermEnrollmentClosed,
  createTermConcluded,
  getSuccessData,
  UserRole,
  ApiErrorId,
  DeliveryMode,
  CreateCoursePayload,
  EnrollmentState,
  CourseState,
  TermState
} from './helpers';

describe('High-Value Functional Tests', () => {
  // T-TERM-01: Registrar opens a term that is already ENROLLMENT_OPEN
  describe('T-TERM-01: Double-open of term', () => {
    it('should reject opening an already open term with ERR_TERM_NOT_ACTIVE', async () => {
      const { term } = await createTermEnrollmentOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      // Try to open it again
      const response = await apiClient.openTermRegistration(
        term.id,
        { revision: term.revision },
        registrarHeaders
      );
      
      expect(response.status).toBe(409);
      const error = response.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_TERM_NOT_ACTIVE);
    });
  });

  // T-TERM-02: Concurrent PATCH :open-registration with stale revision
  describe('T-TERM-02: Concurrent operations with revision conflict', () => {
    it('should reject concurrent open-registration with stale revision', async () => {
      const { term } = await createTermPlanning();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      // First open succeeds
      const response1 = await apiClient.openTermRegistration(
        term.id,
        { revision: term.revision },
        registrarHeaders
      );
      expect(response1.status).toBe(200);
      
      // Second attempt with same (now stale) revision
      const response2 = await apiClient.openTermRegistration(
        term.id,
        { revision: term.revision }, // Using original revision, not updated one
        registrarHeaders
      );
      
      expect(response2.status).toBe(409);
      const error = response2.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_REV_CONFLICT);
    });
  });

  // T-TERM-02B: Revision check ordering for close-registration
  describe('T-TERM-02B: Revision check ordering on close-registration', () => {
    it('should check revision before state when closing registration', async () => {
      const { term } = await createTermPlanning();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      // Try to close registration with valid revision but wrong state (PLANNING)
      const response = await apiClient.closeTermRegistration(
        term.id,
        { revision: term.revision + 1 }, // Wrong revision
        registrarHeaders
      );
      
      // Should get revision error, not state error
      expect(response.status).toBe(409);
      const error = response.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_REV_CONFLICT);
    });
  });

  // T-TERM-03: After closing registration, all OPEN courses become IN_PROGRESS
  describe('T-TERM-03: Cascade behavior on close-registration', () => {
    it('should transition all OPEN courses to IN_PROGRESS when closing registration', async () => {
      const { term } = await createTermEnrollmentOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      
      // Create and publish multiple courses
      const courseCodes = ['CAS101', 'CAS102', 'CAS103'];
      const courseIds: string[] = [];
      
      for (const code of courseCodes) {
        const courseData: CreateCoursePayload = {
          code,
          title: `Cascade Test ${code}`,
          credits: 3,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${code}`
        };
        
        const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
        const course = getSuccessData(createResp);
        
        await apiClient.publishCourse(
          term.id,
          course.id,
          { revision: course.revision },
          professorHeaders
        );
        
        courseIds.push(course.id);
      }
      
      // Close registration
      const termResp = await apiClient.getTerm(term.id, registrarHeaders);
      const termData = getSuccessData(termResp);
      await apiClient.closeTermRegistration(
        term.id,
        { revision: termData.revision },
        registrarHeaders
      );
      
      // Verify all courses are now IN_PROGRESS
      for (const courseId of courseIds) {
        const courseResp = await apiClient.getCourse(term.id, courseId, professorHeaders);
        const course = getSuccessData(courseResp);
        expect(course.state).toBe(CourseState.IN_PROGRESS);
      }
    });
  });

  // T-CRS-01: Professor creates 6th course in same term
  describe('T-CRS-01: Professor course limit', () => {
    it('should reject 6th course creation with ERR_MAX_COURSES_REACHED', async () => {
      const { term } = await createTermEnrollmentOpen();
      const uniqueProfessorId = '66666666-7777-8888-9999-000000000000';
      const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);
      
      // Create 5 courses successfully
      for (let i = 0; i < 5; i++) {
        const courseData: CreateCoursePayload = {
          code: `MAX${i+1}01`,
          title: `Max Course Test ${i+1}`,
          credits: 3,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${i+1}00`
        };
        
        const resp = await apiClient.createCourse(term.id, courseData, professorHeaders);
        expect(resp.status).toBe(201);
      }
      
      // 6th course should fail
      const courseData: CreateCoursePayload = {
        code: 'MAX601',
        title: 'Max Course Test 6',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/max601'
      };
      
      const resp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      expect(resp.status).toBe(409);
      const error = resp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_MAX_COURSES_REACHED);
    });
  });

  // T-CRS-04: Successful publish initializes CourseSeatLedger
  describe('T-CRS-04: Ledger initialization on publish', () => {
    it('should initialize seat ledger with seats_available == capacity on publish', async () => {
      const { term } = await createTermEnrollmentOpen();
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      
      const courseData: CreateCoursePayload = {
        code: 'LED101',
        title: 'Ledger Test',
        credits: 3,
        capacity: 25,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Lecture Hall A'
      };
      
      const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const draftCourse = getSuccessData(createResp);
      
      // Before publishing, available_seats should be 0
      expect(draftCourse.available_seats).toBe(0);
      
      // Publish the course
      const publishResp = await apiClient.publishCourse(
        term.id,
        draftCourse.id,
        { revision: draftCourse.revision },
        professorHeaders
      );
      const publishedCourse = getSuccessData(publishResp);
      
      // After publishing, available_seats should equal capacity
      expect(publishedCourse.available_seats).toBe(25);
      expect(publishedCourse.state).toBe(CourseState.OPEN);
    });
  });

  // T-ENR-01/02: Credit limit enforcement vs Registrar override
  describe('T-ENR-01/02: Credit limit vs registrar override', () => {
    it('should block student self-enrollment over 18 credits but allow registrar override', async () => {
      const { term } = await createTermEnrollmentOpen();
      const uniqueStudentId = 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee';
      const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      
      // Create courses totaling 17 credits (5+5+4+3)
      const coursesData = [
        { code: 'CRD501', credits: 5 },
        { code: 'CRD502', credits: 5 },
        { code: 'CRD401', credits: 4 },
        { code: 'CRD301', credits: 3 }
      ];
      
      // Enroll student in 17 credits
      for (const courseInfo of coursesData) {
        const courseData: CreateCoursePayload = {
          code: courseInfo.code,
          title: `Credit Test ${courseInfo.code}`,
          credits: courseInfo.credits,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${courseInfo.code}`
        };
        
        const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
        const course = getSuccessData(createResp);
        
        await apiClient.publishCourse(
          term.id,
          course.id,
          { revision: course.revision },
          professorHeaders
        );
        
        const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
        expect(enrollResp.status).toBe(201);
      }
      
      // Create a 4-credit course that would push total to 21
      const overloadCourse: CreateCoursePayload = {
        code: 'CRD402',
        title: 'Credit Overload Test',
        credits: 4,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/crd402'
      };
      
      const createResp = await apiClient.createCourse(term.id, overloadCourse, professorHeaders);
      const course = getSuccessData(createResp);
      
      await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        professorHeaders
      );
      
      // T-ENR-01: Student self-enrollment should fail
      const studentEnrollResp = await apiClient.createEnrollment(
        term.id,
        course.id,
        {},
        studentHeaders
      );
      expect(studentEnrollResp.status).toBe(409);
      const error = studentEnrollResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_CREDIT_LIMIT_EXCEEDED);
      
      // T-ENR-02: Registrar enrollment should succeed (override)
      const registrarEnrollResp = await apiClient.createEnrollment(
        term.id,
        course.id,
        { student_id: uniqueStudentId },
        registrarHeaders
      );
      expect(registrarEnrollResp.status).toBe(201);
      const enrollment = getSuccessData(registrarEnrollResp);
      expect(enrollment.state).toBe(EnrollmentState.ENROLLED);
    });
  });

  // T-ENR-04: Waitlist promotion mechanics
  describe('T-ENR-04: Waitlist promotion FIFO mechanics', () => {
    it('should promote oldest waitlisted student when seat becomes available', async () => {
      const { term } = await createTermEnrollmentOpen();
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      
      // Create unique student IDs
      const enrolledStudent = '11111111-2222-3333-4444-555555555555';
      const waitlistStudent1 = '22222222-3333-4444-5555-666666666666';
      const waitlistStudent2 = '33333333-4444-5555-6666-777777777777';
      
      const enrolledHeaders = createHeaders(enrolledStudent, UserRole.STUDENT);
      const waitlist1Headers = createHeaders(waitlistStudent1, UserRole.STUDENT);
      const waitlist2Headers = createHeaders(waitlistStudent2, UserRole.STUDENT);
      
      // Create course with capacity 1
      const courseData: CreateCoursePayload = {
        code: 'WLP101',
        title: 'Waitlist Promotion Test',
        credits: 3,
        capacity: 1,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Small Room'
      };
      
      const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(createResp);
      
      await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        professorHeaders
      );
      
      // First student gets the seat
      const enroll1Resp = await apiClient.createEnrollment(term.id, course.id, {}, enrolledHeaders);
      const enrollment1 = getSuccessData(enroll1Resp);
      expect(enrollment1.state).toBe(EnrollmentState.ENROLLED);
      
      // Next two students get waitlisted (order matters!)
      const enroll2Resp = await apiClient.createEnrollment(term.id, course.id, {}, waitlist1Headers);
      const enrollment2 = getSuccessData(enroll2Resp);
      expect(enrollment2.state).toBe(EnrollmentState.WAITLISTED);
      
      // Small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const enroll3Resp = await apiClient.createEnrollment(term.id, course.id, {}, waitlist2Headers);
      const enrollment3 = getSuccessData(enroll3Resp);
      expect(enrollment3.state).toBe(EnrollmentState.WAITLISTED);
      
      // Drop the enrolled student
      await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment1.id,
        { revision: enrollment1.revision },
        enrolledHeaders
      );
      
      // Check that waitlistStudent1 (first waitlisted) is now enrolled
      const updated2Resp = await apiClient.getEnrollment(
        term.id,
        course.id,
        enrollment2.id,
        waitlist1Headers
      );
      const updated2 = getSuccessData(updated2Resp);
      expect(updated2.state).toBe(EnrollmentState.ENROLLED);
      
      // Check that waitlistStudent2 is still waitlisted
      const updated3Resp = await apiClient.getEnrollment(
        term.id,
        course.id,
        enrollment3.id,
        waitlist2Headers
      );
      const updated3 = getSuccessData(updated3Resp);
      expect(updated3.state).toBe(EnrollmentState.WAITLISTED);
      
      // Verify seat count is back to 0
      const courseResp = await apiClient.getCourse(term.id, course.id, professorHeaders);
      const updatedCourse = getSuccessData(courseResp);
      expect(updatedCourse.available_seats).toBe(0);
    });
  });

  // T-DRP-01: Third drop penalty application
  describe('T-DRP-01: Third drop penalty fee', () => {
    it('should apply $50 penalty on 3rd self-initiated drop', async () => {
      const { term } = await createTermEnrollmentOpen();
      const uniqueStudentId = '44444444-5555-6666-7777-888888888888';
      const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);
      
      // Create 3 courses for drops
      const courses = [];
      for (let i = 0; i < 3; i++) {
        const courseData: CreateCoursePayload = {
          code: `PEN${i+1}01`,
          title: `Penalty Test ${i+1}`,
          credits: 1,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room P${i+1}`
        };
        
        const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
        const course = getSuccessData(createResp);
        
        await apiClient.publishCourse(
          term.id,
          course.id,
          { revision: course.revision },
          professorHeaders
        );
        
        courses.push(course);
      }
      
      // Student enrolls and drops 3 times
      for (let i = 0; i < 3; i++) {
        const enrollResp = await apiClient.createEnrollment(
          term.id,
          courses[i].id,
          {},
          studentHeaders
        );
        const enrollment = getSuccessData(enrollResp);
        
        const dropResp = await apiClient.dropEnrollment(
          term.id,
          courses[i].id,
          enrollment.id,
          { revision: enrollment.revision },
          studentHeaders
        );
        
        expect(dropResp.status).toBe(200);
        
        if (i === 2) {
          // After 3rd drop, verify penalty was applied
          // The student should now owe $50 (5000 cents)
          // Try to pay exactly 5000 cents - should succeed
          const payResp = await apiClient.makePayment(
            term.id,
            uniqueStudentId,
            { amount: 5000 },
            studentHeaders
          );
          expect(payResp.status).toBe(200);
          
          // Try to pay 1 more cent - should fail (overpayment)
          const overpayResp = await apiClient.makePayment(
            term.id,
            uniqueStudentId,
            { amount: 1 },
            studentHeaders
          );
          expect(overpayResp.status).toBe(422);
          const error = overpayResp.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_OVERPAY_NOT_ALLOWED);
        }
      }
    });
  });

  // T-DRP-03: Professor can't drop after enrollment closed
  describe('T-DRP-03: Professor drop permission after enrollment closed', () => {
    it('should reject professor drop when term is ENROLLMENT_CLOSED', async () => {
      const { term } = await createTermEnrollmentOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Create and publish course
      const courseData: CreateCoursePayload = {
        code: 'DRP301',
        title: 'Drop Permission Test',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room D301'
      };
      
      const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(createResp);
      
      await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        professorHeaders
      );
      
      // Student enrolls
      const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      const enrollment = getSuccessData(enrollResp);
      
      // Close registration
      const termResp = await apiClient.getTerm(term.id, registrarHeaders);
      const termData = getSuccessData(termResp);
      await apiClient.closeTermRegistration(
        term.id,
        { revision: termData.revision },
        registrarHeaders
      );
      
      // Professor attempts to drop student
      const dropResp = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        professorHeaders
      );
      
      // Should be rejected per the PRD
      expect(dropResp.status).toBe(409);
      const error = dropResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_REGISTRATION_CLOSED);
    });
  });

  // T-CANC-01: Course cancellation cascade
  describe('T-CANC-01: Course cancellation cascade effects', () => {
    it('should drop all enrollments and refund enrolled students on cancel', async () => {
      const { term } = await createTermEnrollmentOpen();
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      
      // Create unique students
      const enrolledStudent1 = '11111111-2222-3333-4444-444444444444';
      const enrolledStudent2 = '22222222-3333-4444-5555-555555555555';
      const waitlistedStudent = '33333333-4444-5555-6666-666666666666';
      
      const enrolled1Headers = createHeaders(enrolledStudent1, UserRole.STUDENT);
      const enrolled2Headers = createHeaders(enrolledStudent2, UserRole.STUDENT);
      const waitlistedHeaders = createHeaders(waitlistedStudent, UserRole.STUDENT);
      
      // Create course with capacity 2
      const courseData: CreateCoursePayload = {
        code: 'CAN101',
        title: 'Cancellation Test',
        credits: 4,
        capacity: 2,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/can101'
      };
      
      const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(createResp);
      
      await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        professorHeaders
      );
      
      // Get updated course after publish
      const courseResp = await apiClient.getCourse(term.id, course.id, professorHeaders);
      const publishedCourse = getSuccessData(courseResp);
      
      // Two students enroll (fill capacity)
      const enroll1Resp = await apiClient.createEnrollment(term.id, course.id, {}, enrolled1Headers);
      const enrollment1 = getSuccessData(enroll1Resp);
      expect(enrollment1.state).toBe(EnrollmentState.ENROLLED);
      
      const enroll2Resp = await apiClient.createEnrollment(term.id, course.id, {}, enrolled2Headers);
      const enrollment2 = getSuccessData(enroll2Resp);
      expect(enrollment2.state).toBe(EnrollmentState.ENROLLED);
      
      // Third student waitlisted
      const enroll3Resp = await apiClient.createEnrollment(term.id, course.id, {}, waitlistedHeaders);
      const enrollment3 = getSuccessData(enroll3Resp);
      expect(enrollment3.state).toBe(EnrollmentState.WAITLISTED);
      
      // Students make partial payments
      await apiClient.makePayment(term.id, enrolledStudent1, { amount: 20000 }, enrolled1Headers);
      await apiClient.makePayment(term.id, enrolledStudent2, { amount: 30000 }, enrolled2Headers);
      
      // Cancel the course
      await apiClient.cancelCourse(
        term.id,
        course.id,
        { revision: publishedCourse.revision },
        professorHeaders
      );
      
      // Verify all enrollments are dropped
      const updated1Resp = await apiClient.getEnrollment(
        term.id,
        course.id,
        enrollment1.id,
        enrolled1Headers
      );
      const updated1 = getSuccessData(updated1Resp);
      expect(updated1.state).toBe(EnrollmentState.DROPPED);
      
      const updated2Resp = await apiClient.getEnrollment(
        term.id,
        course.id,
        enrollment2.id,
        enrolled2Headers
      );
      const updated2 = getSuccessData(updated2Resp);
      expect(updated2.state).toBe(EnrollmentState.DROPPED);
      
      const updated3Resp = await apiClient.getEnrollment(
        term.id,
        course.id,
        enrollment3.id,
        waitlistedHeaders
      );
      const updated3 = getSuccessData(updated3Resp);
      expect(updated3.state).toBe(EnrollmentState.DROPPED);
      
      // Verify refunds: enrolled students should have reduced balances
      // Student 1: was 40000, paid 20000, refunded 40000 -> balance 0
      const pay1Resp = await apiClient.makePayment(
        term.id,
        enrolledStudent1,
        { amount: 1 },
        enrolled1Headers
      );
      expect(pay1Resp.status).toBe(422); // Overpayment
      
      // Student 2: was 40000, paid 30000, refunded 40000 -> balance 0
      const pay2Resp = await apiClient.makePayment(
        term.id,
        enrolledStudent2,
        { amount: 1 },
        enrolled2Headers
      );
      expect(pay2Resp.status).toBe(422); // Overpayment
      
      // Waitlisted student never owed anything
      const pay3Resp = await apiClient.makePayment(
        term.id,
        waitlistedStudent,
        { amount: 1 },
        waitlistedHeaders
      );
      expect(pay3Resp.status).toBe(422); // Overpayment
    });
  });

  // T-PAY-01: Zero payment validation
  describe('T-PAY-01: Zero payment validation', () => {
    it('should reject payment with amount 0', async () => {
      const { term } = await createTermEnrollmentOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      const payResp = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_A.id,
        { amount: 0 },
        studentHeaders
      );
      
      expect(payResp.status).toBe(422);
      const error = payResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_INVALID_PAYMENT_AMOUNT);
    });
  });

  // T-VIS-01/02/03: Field visibility by role
  describe('T-VIS-01/02/03: Field visibility by role', () => {
    it('should enforce role-based field visibility rules', async () => {
      const { term } = await createTermEnrollmentOpen();
      const professorAHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const professorBHeaders = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      // Professor A creates and publishes a course
      const courseData: CreateCoursePayload = {
        code: 'VIS101',
        title: 'Visibility Test',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room V101'
      };
      
      const createResp = await apiClient.createCourse(term.id, courseData, professorAHeaders);
      const course = getSuccessData(createResp);
      
      await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        professorAHeaders
      );
      
      // Student enrolls
      await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      
      // T-VIS-01: Student GET /courses should not expose enrolled_count
      const studentListResp = await apiClient.listCourses(term.id, {}, studentHeaders);
      const studentCourses = getSuccessData(studentListResp);
      const studentCourseView = studentCourses.find((c: any) => c.id === course.id);
      expect(studentCourseView).toBeDefined();
      expect(studentCourseView!.enrolled_count).toBeUndefined();
      expect(studentCourseView!.waitlist_count).toBeUndefined();
      
      // T-VIS-02: Professor fetches their course and sees enrollments array
      const profCourseResp = await apiClient.getCourse(term.id, course.id, professorAHeaders);
      const profCourseView = getSuccessData(profCourseResp) as any; // PRD states enrollments array is included for professor's own course
      expect(profCourseView.enrollments).toBeDefined();
      expect(Array.isArray(profCourseView.enrollments)).toBe(true);
      expect(profCourseView.enrollments.length).toBe(1);
      expect(profCourseView.enrolled_count).toBe(1);
      
      // T-VIS-03: Professor B creates a draft course
      const draftCourseData: CreateCoursePayload = {
        code: 'VIS201',
        title: 'Draft Visibility Test',
        credits: 3,
        capacity: 5,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/vis201'
      };
      
      const draftResp = await apiClient.createCourse(term.id, draftCourseData, professorBHeaders);
      const draftCourse = getSuccessData(draftResp);
      
      // Professor A tries to fetch Professor B's draft course
      const otherProfResp = await apiClient.getCourse(term.id, draftCourse.id, professorAHeaders);
      expect(otherProfResp.status).toBe(404);
      const error = otherProfResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_COURSE_NOT_FOUND);
      
      // Registrar can see everything
      const registrarCourseResp = await apiClient.getCourse(term.id, course.id, registrarHeaders);
      const registrarCourseView = getSuccessData(registrarCourseResp) as any;
      expect(registrarCourseView.enrollments).toBeDefined();
      expect(registrarCourseView.enrolled_count).toBe(1);
      expect(registrarCourseView.waitlist_count).toBe(0);
    });
  });
});
/**
 * Enrollment and Drop Management Tests
 * 
 * Tests for student enrollment, waitlisting, drops, credit limits, and drop penalties.
 * Assigned test cases: E1-E12 (enrollment) and D1-D9 (drops)
 */

import {
  apiClient,
  createHeaders,
  TEST_USERS,
  createCourseOpen,
  createCourseDraft,
  createCourseFullCapacity,
  createCourseWithWaitlist,
  createStudentEnrolledCourse,
  createStudentMaxCreditLoad,
  createTermEnrollmentOpen,
  createTermConcluded,
  createCourseCompleted,
  createStudentWithDroppedCourses,
  getStudentEnrollment,
  validateErrorEnvelope,
  handleApiResponse,
  getSuccessData,
  assertEnrollment,
  UserRole,
  EnrollmentState,
  ApiErrorId,
  calculateCourseCost,
  DROP_PENALTY_FEE,
  DeliveryMode
} from './helpers';
import { v4 as uuidv4 } from 'uuid';

describe('Enrollment Management Tests', () => {
  describe('E1: Enroll in Course - Success (Seat Available)', () => {
    it('should successfully enroll student when seat is available', async () => {
      // Setup: Course with available seats
      const { term, course } = await createCourseOpen('CS101', 5);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Action: Student enrolls in course
      const response = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      
      // Assertions
      const enrollment = handleApiResponse(response, 201);
      assertEnrollment(enrollment);
      expect(enrollment.student_id).toBe(TEST_USERS.STUDENT_A.id);
      expect(enrollment.course_id).toBe(course.id);
      expect(enrollment.state).toBe(EnrollmentState.ENROLLED);
      
      // Verify course seat count decremented
      const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const courseResponse = await apiClient.getCourse(term.id, course.id, regHeaders);
      const updatedCourse = getSuccessData(courseResponse);
      expect(updatedCourse.available_seats).toBe(course.capacity - 1);
      expect(updatedCourse.enrolled_count).toBe(1);
      
      // Note: Tuition ledger verification would require additional endpoint
    });
  });

  describe('E2: Enroll in Course - Success (Goes to Waitlist)', () => {
    it('should place student on waitlist when course is full', async () => {
      // Setup: Course at full capacity
      const { term, course } = await createCourseFullCapacity();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);
      
      // Action: Student attempts to enroll in full course
      const response = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      
      // Assertions
      const enrollment = handleApiResponse(response, 201);
      assertEnrollment(enrollment);
      expect(enrollment.student_id).toBe(TEST_USERS.STUDENT_C.id);
      expect(enrollment.state).toBe(EnrollmentState.WAITLISTED);
      
      // Verify course counts
      const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const courseResponse = await apiClient.getCourse(term.id, course.id, regHeaders);
      const updatedCourse = getSuccessData(courseResponse);
      expect(updatedCourse.available_seats).toBe(0); // Still full
      expect(updatedCourse.waitlist_count).toBeGreaterThan(0);
    });
  });

  describe('E3: Enroll in Course - Registrar Override Credit Limit', () => {
    it('should allow registrar to enroll student beyond credit limit', async () => {
      // Setup: Student at max credit load and a new course
      const { term: studentTerm } = await createStudentMaxCreditLoad(TEST_USERS.STUDENT_A.id);
      
      // Create a course in the same term as the student's existing courses
      const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const courseResponse = await apiClient.createCourse(studentTerm.id, {
        code: 'CS999',
        title: 'Extra Course',
        credits: 3,
        capacity: 5,
        professor_id: TEST_USERS.PROFESSOR_A.id,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room 999'
      }, profHeaders);
      const draftCourse = getSuccessData(courseResponse);
      
      // Publish the course
      const publishResponse = await apiClient.publishCourse(studentTerm.id, draftCourse.id, { revision: draftCourse.revision }, profHeaders);
      const course = getSuccessData(publishResponse);
      
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      // Action: Registrar enrolls student despite credit limit
      const response = await apiClient.createEnrollment(
        studentTerm.id, 
        course.id, 
        { student_id: TEST_USERS.STUDENT_A.id }, 
        registrarHeaders
      );
      
      // Assertions
      const enrollment = handleApiResponse(response, 201);
      expect(enrollment.state).toBe(EnrollmentState.ENROLLED);
      expect(enrollment.student_id).toBe(TEST_USERS.STUDENT_A.id);
    });
  });

  describe('E4: Enroll in Course - Credit Limit Exceeded (Student)', () => {
    it('should block student from exceeding 18-credit term limit', async () => {
      // Setup: Student at max credit load
      const { term: studentTerm } = await createStudentMaxCreditLoad(TEST_USERS.STUDENT_A.id);
      
      // Create a course in the same term as the student's existing courses
      const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const courseResponse = await apiClient.createCourse(studentTerm.id, {
        code: 'CS999',
        title: 'Extra Course',
        credits: 3,
        capacity: 3,
        professor_id: TEST_USERS.PROFESSOR_A.id,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/cs999'
      }, profHeaders);
      const draftCourse = getSuccessData(courseResponse);
      
      // Publish the course
      const publishResponse = await apiClient.publishCourse(studentTerm.id, draftCourse.id, { revision: draftCourse.revision }, profHeaders);
      const course = getSuccessData(publishResponse);
      
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Action: Student attempts to enroll beyond credit limit
      const response = await apiClient.createEnrollment(studentTerm.id, course.id, {}, studentHeaders);
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_CREDIT_LIMIT_EXCEEDED });
    });
  });

  describe('E5: Enroll in Course - Unauthorized Role', () => {
    it('should reject professor attempt to use enrollment endpoint', async () => {
      // Setup: Open course
      const { term, course } = await createCourseOpen();
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);
      
      // Action: Professor attempts to enroll a student
      const response = await apiClient.createEnrollment(
        term.id, 
        course.id, 
        { student_id: TEST_USERS.STUDENT_A.id }, 
        professorHeaders
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_UNAUTHORIZED_ROLE });
    });
  });

  describe('E6: Enroll in Course - Student ID Conflict', () => {
    it('should reject student attempting to enroll another student', async () => {
      // Setup: Open course
      const { term, course } = await createCourseOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Action: Student tries to enroll someone else
      const response = await apiClient.createEnrollment(
        term.id, 
        course.id, 
        { student_id: TEST_USERS.STUDENT_B.id }, 
        studentHeaders
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_FORBIDDEN });
    });
  });

  describe('E7: Enroll in Course - Registrar Missing Student ID', () => {
    it('should require student_id when registrar enrolls', async () => {
      // Setup: Open course
      const { term, course } = await createCourseOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      // Action: Registrar attempts enrollment without student_id
      const response = await apiClient.createEnrollment(term.id, course.id, {}, registrarHeaders);
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_MISSING_REQUIRED_FIELD });
    });
  });

  describe('E8: Enroll in Course - Student Not Found', () => {
    it('should reject enrollment for non-existent student', async () => {
      // Setup: Open course
      const { term, course } = await createCourseOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      // Action: Registrar attempts to enroll non-existent student
      const response = await apiClient.createEnrollment(
        term.id, 
        course.id, 
        { student_id: uuidv4() }, // Valid UUID that doesn't exist
        registrarHeaders
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_STUDENT_NOT_FOUND });
    });
  });

  describe('E9: Enroll in Course - Course Not Found', () => {
    it('should reject enrollment for invalid course ID', async () => {
      // Setup: Valid term
      const { term } = await createTermEnrollmentOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Action: Student attempts to enroll in non-existent course
      const response = await apiClient.createEnrollment(term.id, uuidv4(), {}, studentHeaders);
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_COURSE_NOT_FOUND });
    });
  });

  describe('E10: Enroll in Course - Course Not Open', () => {
    it('should reject enrollment in draft course', async () => {
      // Setup: Term open but course still in DRAFT
      const { term } = await createTermEnrollmentOpen();
      const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      // Create course but don't publish it (stays DRAFT)
      const createResponse = await apiClient.createCourse(
        term.id,
        {
          code: 'DRF001',
          title: 'Draft Course',
          credits: 3,
          capacity: 10,
          professor_id: TEST_USERS.PROFESSOR_A.id,
          delivery_mode: DeliveryMode.HYBRID,
          location: 'Room 001',
          online_link: 'https://example.com/drf001'
        },
        regHeaders
      );
      const draftCourse = getSuccessData(createResponse);
      
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Action: Student attempts to enroll in draft course
      const response = await apiClient.createEnrollment(term.id, draftCourse.id, {}, studentHeaders);
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_COURSE_WRONG_STATE });
    });
  });

  describe('E11: Enroll in Course - Registration Closed', () => {
    it('should reject enrollment when term registration is closed', async () => {
      // Setup: Create course in open term, then close registration
      const { term: openTerm, course } = await createCourseOpen();
      
      // Close term registration
      const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      await apiClient.closeTermRegistration(openTerm.id, { revision: openTerm.revision }, regHeaders);
      
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Action: Student attempts to enroll after registration closed
      const response = await apiClient.createEnrollment(openTerm.id, course.id, {}, studentHeaders);
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_REGISTRATION_CLOSED });
    });
  });

  describe('E12: Enroll in Course - Already Enrolled', () => {
    it('should reject duplicate enrollment attempt', async () => {
      // Setup: Student already enrolled in a course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      // Action: Student attempts to enroll again in same course
      const response = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ALREADY_ENROLLED });
    });
  });

  describe('E13: Enroll in Course - Term in Planning State', () => {
    it('should reject enrollment when term is in PLANNING state', async () => {
      // Setup: Course in a planning term
      const { term, course } = await createCourseDraft();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      // Action: Student attempts to enroll in planning term
      const response = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_REGISTRATION_CLOSED });
    });
  });

  describe('E14: Enroll in Course - Invalid UUID Format', () => {
    it('should reject enrollment with malformed termId', async () => {
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      // Action: Student attempts to enroll with invalid termId
      const response = await apiClient.createEnrollment('invalid-uuid', uuidv4(), {}, studentHeaders);

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
    });

    it('should reject enrollment with malformed courseId', async () => {
      const { term } = await createTermEnrollmentOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      // Action: Student attempts to enroll with invalid courseId
      const response = await apiClient.createEnrollment(term.id, 'invalid-uuid', {}, studentHeaders);

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
    });

    it('should reject registrar enrollment with malformed student_id', async () => {
      const { term, course } = await createCourseOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

      // Action: Registrar attempts to enroll with invalid student_id
      const response = await apiClient.createEnrollment(
        term.id,
        course.id,
        { student_id: 'invalid-uuid' },
        registrarHeaders
      );

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
    });
  });

  describe('E15: Registrar Override - Enroll When Registration Closed', () => {
    it('should allow registrar to enroll student when registration is closed', async () => {
      // Setup: Course in a closed term
      const { term, course } = await createCourseOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

      // Close the term registration
      const closeResponse = await apiClient.closeTermRegistration(
        term.id,
        { revision: term.revision },
        registrarHeaders
      );
      const closedTerm = getSuccessData(closeResponse);

      // Action: Registrar enrolls student in closed term
      const response = await apiClient.createEnrollment(
        term.id,
        course.id,
        { student_id: TEST_USERS.STUDENT_A.id },
        registrarHeaders
      );

      // Assertions
      const enrollment = handleApiResponse(response, 201);
      assertEnrollment(enrollment);
      expect(enrollment.student_id).toBe(TEST_USERS.STUDENT_A.id);
      expect(enrollment.state).toBe(EnrollmentState.ENROLLED);
    });
  });

  describe('E16: Enroll in Course - Term Concluded', () => {
    it('should reject enrollment when term is concluded with ERR_TERM_NOT_ACTIVE', async () => {
      // Setup: Course in a concluded term
      const { term, course } = await createCourseOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

      // Close and conclude the term
      const closeResponse = await apiClient.closeTermRegistration(
        term.id,
        { revision: term.revision },
        registrarHeaders
      );
      const closedTerm = getSuccessData(closeResponse);

      await apiClient.concludeTerm(
        closedTerm.id,
        { revision: closedTerm.revision },
        registrarHeaders
      );

      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      // Action: Student attempts to enroll in concluded term
      const response = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

      // Assertions: Should return ERR_TERM_NOT_ACTIVE, not ERR_REGISTRATION_CLOSED
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE });
    });
  });
});

describe('Drop Management Tests', () => {
  describe('D1: Drop Enrollment - Student Drops Enrolled Course', () => {
    it('should successfully drop enrolled course and refund tuition', async () => {
      // Setup: Student enrolled in a course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Action: Student drops the course
      const response = await apiClient.dropEnrollment(
        term.id, 
        course.id, 
        enrollment.id, 
        { revision: enrollment.revision }, 
        studentHeaders
      );
      
      // Assertions
      const droppedEnrollment = handleApiResponse(response, 200);
      expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);
      
      // Verify seat freed
      const courseResponse = await apiClient.getCourse(term.id, course.id, studentHeaders);
      const updatedCourse = getSuccessData(courseResponse);
      expect(updatedCourse.available_seats).toBe(course.capacity); // Back to full capacity
    });
  });

  describe('D2: Drop Enrollment - Student Drops Waitlisted Course', () => {
    it('should successfully remove student from waitlist', async () => {
      // Setup: Course with waitlisted student
      const { term, course, enrollments } = await createCourseWithWaitlist();
      const waitlistedEnrollment = enrollments.studentC; // StudentC is waitlisted
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);
      
      // Action: Waitlisted student drops
      const response = await apiClient.dropEnrollment(
        term.id,
        course.id,
        waitlistedEnrollment.id,
        { revision: waitlistedEnrollment.revision },
        studentHeaders
      );
      
      // Assertions
      const droppedEnrollment = handleApiResponse(response, 200);
      expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);
      
      // Verify waitlist count decreased but seats unchanged
      const courseResponse = await apiClient.getCourse(term.id, course.id, studentHeaders);
      const updatedCourse = getSuccessData(courseResponse);
      expect(updatedCourse.available_seats).toBe(0); // Still no seats available
    });
  });

  describe('D3: Drop Enrollment - Professor Drops Student from Course', () => {
    it('should allow instructor to drop student without penalty', async () => {
      // Setup: Student enrolled in professor's course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      
      // Action: Professor drops the student
      const response = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        profHeaders
      );
      
      // Assertions
      const droppedEnrollment = handleApiResponse(response, 200);
      expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);
      
      // Note: Professor-initiated drops should not count toward student's drop limit
      // This would require additional verification through a student drop count endpoint
    });
  });

  describe('D4: Drop Enrollment - Unauthorized Actor', () => {
    it('should reject unauthorized user attempting to drop enrollment', async () => {
      // Setup: Student A enrolled in a course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      
      // Action: Different student attempts to drop Student A's enrollment
      const unauthorizedHeaders = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);
      const response = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        unauthorizedHeaders
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_PERMISSION_DENIED });
    });
  });

  describe('D5: Drop Enrollment - Already Dropped/Completed', () => {
    it('should reject dropping already completed enrollment', async () => {
      // Setup: Create completed course with completed enrollment
      const { term, course, enrollments } = await createCourseCompleted();
      const completedEnrollment = enrollments[0]; // Student A's enrollment
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Action: Attempt to drop completed enrollment
      const response = await apiClient.dropEnrollment(
        term.id,
        course.id,
        completedEnrollment.id,
        { revision: completedEnrollment.revision },
        studentHeaders
      );
      
      // Assertions
      // According to PRD §2.6, term state gating errors take precedence over enrollment state.
      // Since the term is CONCLUDED, the expected error is ERR_TERM_NOT_ACTIVE.
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE });
    });
    
    it('should reject dropping already dropped enrollment', async () => {
      // Setup: Create enrollment and drop it
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // First drop (should succeed)
      const dropResponse = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        studentHeaders
      );
      const droppedEnrollment = getSuccessData(dropResponse);
      
      // Action: Attempt to drop again
      const response = await apiClient.dropEnrollment(
        term.id,
        course.id,
        droppedEnrollment.id,
        { revision: droppedEnrollment.revision },
        studentHeaders
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ENROLLMENT_WRONG_STATE });
    });
  });

  describe('D6: Drop Enrollment - Term Concluded (No Student Drop)', () => {
    it('should reject student drop after term concluded', async () => {
      // Setup: Create and conclude a term with enrolled student
      const { term: concludedTerm } = await createTermConcluded();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Use valid UUIDs that don't exist in the system
      const fakeEnrollmentId = uuidv4();
      const fakeCourseId = uuidv4();
      
      // Action: Student attempts to drop after term concluded
      const response = await apiClient.dropEnrollment(
        concludedTerm.id,
        fakeCourseId,
        fakeEnrollmentId,
        { revision: 0 },
        studentHeaders
      );
      
      // Assertions: Should get ERR_TERM_NOT_ACTIVE or ERR_ENROLLMENT_NOT_FOUND
      // The spec indicates term state is checked first
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE });
    });
  });

  describe('D7: Drop Enrollment - Drop Limit Exceeded', () => {
    it('should block student from dropping 4th course', async () => {
      // Setup: Student has already dropped 3 courses
      const { term, droppedCourses, nextCourse } = await createStudentWithDroppedCourses(3, TEST_USERS.STUDENT_A.id);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Get the enrollment for the next course
      const enrollment = await getStudentEnrollment(term.id, nextCourse.id, TEST_USERS.STUDENT_A.id);
      expect(enrollment).toBeTruthy();
      
      // Action: Attempt to drop 4th course (should be blocked)
      const response = await apiClient.dropEnrollment(
        term.id,
        nextCourse.id,
        enrollment!.id,
        { revision: enrollment!.revision },
        studentHeaders
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_TOO_MANY_DROPS });
    });
  });

  describe('D8: Drop Enrollment - Drop Penalty Fee on 3rd Drop', () => {
    it('should apply $50 penalty fee on 3rd drop', async () => {
      // Setup: Student has already dropped 2 courses
      const { term, droppedCourses, nextCourse } = await createStudentWithDroppedCourses(2, TEST_USERS.STUDENT_A.id);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Get the enrollment for the next course (this will be the 3rd drop)
      const enrollment = await getStudentEnrollment(term.id, nextCourse.id, TEST_USERS.STUDENT_A.id);
      expect(enrollment).toBeTruthy();
      
      // Note: We can't directly verify ledger balance without an endpoint
      // But we can verify the drop succeeds and calculate expected balance
      
      // Action: Drop the 3rd course (should succeed with penalty)
      const response = await apiClient.dropEnrollment(
        term.id,
        nextCourse.id,
        enrollment!.id,
        { revision: enrollment!.revision },
        studentHeaders
      );
      
      // Assertions
      const droppedEnrollment = handleApiResponse(response, 200);
      expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);
      
      // Verify we can attempt a payment to indirectly confirm penalty was applied
      // Expected balance after 3 drops:
      // - 3 courses enrolled = 3 * 3 credits * $100 = $900
      // - 3 courses dropped (refunded) = -$900
      // - Penalty on 3rd drop = +$50
      // - Net balance = $50
      
      // Try to pay exactly $50 (should succeed if penalty was applied)
      const paymentResponse = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_A.id,
        { amount: DROP_PENALTY_FEE }, // $50 penalty fee
        studentHeaders
      );
      
      const paymentResult = handleApiResponse(paymentResponse, 200);
      expect(paymentResult.new_balance).toBe(0); // Should be paid in full
    });
    
    it('should not apply penalty on drops 1 and 2', async () => {
      // Setup: Student drops first course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_B.id);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);
      
      // Drop first course
      const dropResponse = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        studentHeaders
      );
      
      // Verify drop succeeded
      const droppedEnrollment = handleApiResponse(dropResponse, 200);
      expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);
      
      // Try to pay $0 (should succeed if no penalty and full refund)
      const paymentResponse = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_B.id,
        { amount: 1 }, // Try to pay $0.01
        studentHeaders
      );
      
      // Should get overpayment error since balance is 0
      validateErrorEnvelope(paymentResponse, { expectedErrorId: ApiErrorId.ERR_OVERPAY_NOT_ALLOWED });
    });
  });

  describe('D9: Drop Enrollment - Revision Conflict', () => {
    it('should reject drop with stale revision', async () => {
      // Setup: Student enrolled in course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Action: Attempt drop with outdated revision
      const response = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision - 1 }, // Stale revision
        studentHeaders
      );
      
      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_REV_CONFLICT });
    });
  });

  describe('D10: Drop Enrollment - Invalid UUID Format', () => {
    it('should reject drop with malformed termId', async () => {
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      // Action: Student attempts to drop with invalid termId
      const response = await apiClient.dropEnrollment(
        'invalid-uuid',
        uuidv4(),
        uuidv4(),
        { revision: 0 },
        studentHeaders
      );

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
    });

    it('should reject drop with malformed courseId', async () => {
      const { term } = await createTermEnrollmentOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      // Action: Student attempts to drop with invalid courseId
      const response = await apiClient.dropEnrollment(
        term.id,
        'invalid-uuid',
        uuidv4(),
        { revision: 0 },
        studentHeaders
      );

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
    });

    it('should reject drop with malformed enrollmentId', async () => {
      const { term, course } = await createCourseOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      // Action: Student attempts to drop with invalid enrollmentId
      const response = await apiClient.dropEnrollment(
        term.id,
        course.id,
        'invalid-uuid',
        { revision: 0 },
        studentHeaders
      );

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
    });
  });

  describe('D11: Drop Enrollment - After Registration Closes', () => {
    it('should block student from dropping after registration closes', async () => {
      // Setup: Student enrolled in course, then registration closes
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

      // Close registration
      await apiClient.closeTermRegistration(term.id, { revision: term.revision }, registrarHeaders);

      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      // Action: Student attempts to drop after registration closed
      const response = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        studentHeaders
      );

      // Assertions
      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_REGISTRATION_CLOSED });
    });

    it('should allow registrar to drop after registration closes', async () => {
      // Setup: Student enrolled in course, then registration closes
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

      // Close registration
      await apiClient.closeTermRegistration(term.id, { revision: term.revision }, registrarHeaders);

      // Action: Registrar drops student after registration closed
      const response = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        registrarHeaders
      );

      // Assertions
      const droppedEnrollment = handleApiResponse(response, 200);
      expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);
    });
  });

  describe('D12: Professor Drop - No Student Drop Count Impact', () => {
    it('should not count professor-initiated drops toward student drop limit', async () => {
      // Setup: Student enrolled in course
      const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

      // Action: Professor drops the student
      const dropResponse = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        professorHeaders
      );

      // Verify drop succeeded
      const droppedEnrollment = handleApiResponse(dropResponse, 200);
      expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);

      // Setup: Student enrolls in another course to test drop count
      // Create a new course in the same term with a different professor
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

      // Register professor B in the system by making an API call
      const profBHeaders = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);
      await apiClient.getTerm(term.id, profBHeaders);

      const newCourseResponse = await apiClient.createCourse(
        term.id,
        {
          code: 'CS103',
          title: 'Test Course 2',
          credits: 3,
          capacity: 2,
          professor_id: TEST_USERS.PROFESSOR_B.id,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: 'Room 103'
        },
        registrarHeaders
      );
      const newCourse = getSuccessData(newCourseResponse);

      // Publish the course
      await apiClient.publishCourse(term.id, newCourse.id, { revision: newCourse.revision }, profBHeaders);

      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      const enrollResponse = await apiClient.createEnrollment(term.id, newCourse.id, {}, studentHeaders);
      const newEnrollment = getSuccessData(enrollResponse);

      // Action: Student drops their own enrollment (should be allowed since professor drop didn't count)
      const studentDropResponse = await apiClient.dropEnrollment(
        term.id,
        newCourse.id,
        newEnrollment.id,
        { revision: newEnrollment.revision },
        studentHeaders
      );

      // Assertions: Student drop should succeed (no drop limit reached)
      const studentDroppedEnrollment = handleApiResponse(studentDropResponse, 200);
      expect(studentDroppedEnrollment.state).toBe(EnrollmentState.DROPPED);
    });
  });
});
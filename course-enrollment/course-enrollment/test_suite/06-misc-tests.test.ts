/**
 * Gap Tests - Additional test cases to ensure comprehensive coverage
 * 
 * These tests cover edge cases that may not have been covered in the main test suites.
 */

import {
  apiClient,
  createHeaders,
  TEST_USERS,
  createTermEnrollmentOpen,
  createTermPlanning,
  createTermEnrollmentClosed,
  getSuccessData,
  UserRole,
  EnrollmentState,
  TermState,
  CourseState,
  ApiErrorId,
  DeliveryMode,
  CreateCoursePayload
} from './helpers';

describe('Gap Tests', () => {
  describe('TC-GAP-01: Drops rejected when term not in ENROLLMENT_OPEN', () => {
    it('should reject drop attempt when term is ENROLLMENT_CLOSED', async () => {
      // Setup: Create term, open it, create course, enroll student
      const { term } = await createTermEnrollmentOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      
      // Create and publish a course
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const courseData: CreateCoursePayload = {
        code: 'CS101',
        title: 'Intro to CS',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room 101'
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(courseResp);
      
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
      
      // Student enrolls
      const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      const enrollment = getSuccessData(enrollResp);
      expect(enrollment.state).toBe(EnrollmentState.ENROLLED);
      
      // Close registration
      const termResp = await apiClient.getTerm(term.id, registrarHeaders);
      const termData = getSuccessData(termResp);
      await apiClient.closeTermRegistration(term.id, { revision: termData.revision }, registrarHeaders);
      
      // Attempt to drop
      const dropResp = await apiClient.dropEnrollment(
        term.id, 
        course.id, 
        enrollment.id, 
        { revision: enrollment.revision }, 
        studentHeaders
      );
      
      // Should fail with 409
      expect(dropResp.status).toBe(409);
      const error = dropResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_REGISTRATION_CLOSED);
    });
  });

  describe('TC-GAP-02: Admin drops don\'t count toward student drop limit', () => {
    it('should allow student to drop after 3 admin drops without penalty', async () => {
      const { term } = await createTermEnrollmentOpen();
      // Use a unique student for this test
      const uniqueStudentId = 'b1111111-2222-3333-4444-555555555555';
      const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
      // Use multiple professors to avoid hitting course limits
      const professor1Id = 'a1111111-2222-3333-4444-555555555555';
      const professor1Headers = createHeaders(professor1Id, UserRole.PROFESSOR);
      const professor2Id = 'a1111111-2222-3333-4444-666666666666';
      const professor2Headers = createHeaders(professor2Id, UserRole.PROFESSOR);
      
      // Create and enroll student in 3 courses with professor1, then have professor drop them
      for (let i = 0; i < 3; i++) {
        // Create course as professor1
        const courseData: CreateCoursePayload = {
          code: `GP${i + 1}${String(i + 1).padStart(2, '0')}`,
          title: `Gap Test Admin Drop Course ${i + 1}`,
          credits: 1,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${500 + i}`
        };
        const courseResp = await apiClient.createCourse(term.id, courseData, professor1Headers);
        const course = getSuccessData(courseResp);
        
        // Publish course
        await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professor1Headers);
        
        // Student self-enrolls in the course
        const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
        const enrollment = getSuccessData(enrollResp);
        
        // Professor drops the student (admin action)
        await apiClient.dropEnrollment(
          term.id,
          course.id,
          enrollment.id,
          { revision: enrollment.revision },
          professor1Headers
        );
      }
      
      // Now use professor2 for the remaining courses to avoid hitting the 5-course limit
      // Student enrolls in a 4th course and tries to drop it themselves
      const courseData: CreateCoursePayload = {
        code: 'GP401',
        title: 'Gap Test Student Drop Course',
        credits: 1,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/gap2401'
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professor2Headers);
      const course = getSuccessData(courseResp);
      
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professor2Headers);
      
      const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      const enrollment = getSuccessData(enrollResp);
      
      // Student drops the course (should be their 1st self-initiated drop)
      const dropResp = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        studentHeaders
      );
      
      // Should succeed with 200 OK (not blocked)
      expect(dropResp.status).toBe(200);
      const droppedEnrollment = getSuccessData(dropResp);
      expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);
      
      // Verify it counted as only the 1st drop (no penalty) by doing 2 more drops
      // and checking that penalty is applied on the 3rd self-drop
      for (let i = 0; i < 2; i++) {
        const cd: CreateCoursePayload = {
          code: `GP5${String(i + 1).padStart(2, '0')}`,
          title: `Gap Test Student Drop Course ${i + 2}`,
          credits: 1,
          capacity: 10,
          delivery_mode: DeliveryMode.HYBRID,
          location: `Room 60${i}`,
          online_link: `https://example.com/gap25${i + 1}`
        };
        const cr = await apiClient.createCourse(term.id, cd, professor2Headers);
        const c = getSuccessData(cr);
        await apiClient.publishCourse(term.id, c.id, { revision: c.revision }, professor2Headers);
        const er = await apiClient.createEnrollment(term.id, c.id, {}, studentHeaders);
        const e = getSuccessData(er);
        
        if (i === 1) {
          // This should be the 3rd self-drop, expect penalty
          // We can't directly check balance, but if implementation wrongly counted
          // admin drops, this would be blocked with ERR_TOO_MANY_DROPS
          const dr = await apiClient.dropEnrollment(
            term.id,
            c.id,
            e.id,
            { revision: e.revision },
            studentHeaders
          );
          expect(dr.status).toBe(200); // Should succeed (3rd drop allowed with penalty)
        } else {
          // 2nd self-drop
          await apiClient.dropEnrollment(
            term.id,
            c.id,
            e.id,
            { revision: e.revision },
            studentHeaders
          );
        }
      }
    });
  });

  describe('TC-GAP-03: Detect missing refund when ledger balance is lower than course cost', () => {
    it('should refund the student up to the amount they paid', async () => {
      const { term } = await createTermEnrollmentOpen();
      // Use unique IDs for this test
      const uniqueStudentId = 'b2222222-3333-4444-5555-666666666666';
      const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
      const uniqueProfessorId = 'a2222222-3333-4444-5555-666666666666';
      const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);
  
      // Create and publish a 4-credit course
      const courseData: CreateCoursePayload = {
        code: 'GAP301',
        title: 'Gap Test Finance',
        credits: 4,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/gap301'
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
  
      // Student enrolls (balance becomes 40000)
      const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      const enrollment = getSuccessData(enrollResp);
  
      // Student pays 38000 (balance becomes 2000)
      await apiClient.makePayment(term.id, uniqueStudentId, { amount: 38000 }, studentHeaders);
  
      // Student drops the course
      await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision }, studentHeaders);
  
      // The test should assert that the payment fails if the balance is 0.
      const paymentResp = await apiClient.makePayment(term.id, uniqueStudentId, { amount: 1 }, studentHeaders);
      expect(paymentResp.status).toBe(422);
      const error = paymentResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_OVERPAY_NOT_ALLOWED);
    });
  });
  
  describe('TC-GAP-04: Confirm professor course-limit ignores CANCELLED and COMPLETED courses', () => {
      it('should allow professor to create a new course after cancelling one of their 5 courses', async () => {
          const { term } = await createTermEnrollmentOpen();
          // Use a unique professor for this test
          const uniqueProfessorId = 'a3333333-4444-5555-6666-777777777777';
          const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);
  
          // Professor creates 5 courses
          for (let i = 0; i < 5; i++) {
              const courseData: CreateCoursePayload = {
                  code: `GPC${i + 1}${String(i + 1).padStart(2, '0')}`,
                  title: `Gap Test Course ${i + 1}`,
                  credits: 3,
                  capacity: 10,
                  delivery_mode: DeliveryMode.IN_PERSON,
                  location: `Room ${101 + i}`
              };
              const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
              expect(courseResp.status).toBe(201);
          }
  
          // Cancel one course
          const coursesResp = await apiClient.listCourses(term.id, {}, professorHeaders);
          const courses = getSuccessData(coursesResp);
          const courseToCancel = courses[0];
          await apiClient.cancelCourse(term.id, courseToCancel.id, { revision: courseToCancel.revision }, professorHeaders);
  
          // Professor attempts to create a new course
          const newCourseData: CreateCoursePayload = {
              code: 'GPC601',
              title: 'Gap Test New Course',
              credits: 3,
              capacity: 10,
              delivery_mode: DeliveryMode.ONLINE,
              online_link: 'https://example.com/gap4new'
          };
          const newCourseResp = await apiClient.createCourse(term.id, newCourseData, professorHeaders);
          expect(newCourseResp.status).toBe(201);
      });
  });
  
  describe('TC-GAP-05: Validate strict unknown-field rejection on enrollment creation', () => {
      it('should reject enrollment with unknown fields', async () => {
          const { term } = await createTermEnrollmentOpen();
          const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
          const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
  
          // Create and publish a course
          const courseData: CreateCoursePayload = {
              code: 'SEC101',
              title: 'Security',
              credits: 3,
              capacity: 10,
              delivery_mode: DeliveryMode.IN_PERSON,
              location: 'Room 101'
          };
          const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
          const course = getSuccessData(courseResp);
          await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
  
          // Student attempts to enroll with an unknown field
          const enrollResp = await apiClient.createEnrollment(term.id, course.id, { student_id: TEST_USERS.STUDENT_A.id, foo: 'bar' } as any, studentHeaders);
          expect(enrollResp.status).toBe(400);
          const error = enrollResp.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_UNKNOWN_FIELD);
      });
  });
  
  describe('TC-GAP-07: Professor may NOT drop a student after registration is closed', () => {
      it('should reject drop attempt by professor when term is ENROLLMENT_CLOSED', async () => {
          const { term } = await createTermEnrollmentOpen();
          const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
          const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
          const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
  
          // Create and publish a course
          const courseData: CreateCoursePayload = {
              code: 'CS101',
              title: 'Intro to CS',
              credits: 3,
              capacity: 1,
              delivery_mode: DeliveryMode.IN_PERSON,
              location: 'Room 101'
          };
          const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
          const course = getSuccessData(courseResp);
          await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
  
          // Student enrolls
          const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
          const enrollment = getSuccessData(enrollResp);
  
          // Close registration
          const termResp = await apiClient.getTerm(term.id, registrarHeaders);
          const termData = getSuccessData(termResp);
          await apiClient.closeTermRegistration(term.id, { revision: termData.revision }, registrarHeaders);
  
          // Professor attempts to drop the student
          const dropResp = await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision }, professorHeaders);
          expect(dropResp.status).toBe(409);
          const error = dropResp.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_REGISTRATION_CLOSED);
      });
  });
  
  describe('TC-GAP-08: Even Registrar cannot drop once the term is CONCLUDED', () => {
      it('should reject drop attempt by registrar when term is CONCLUDED', async () => {
          const { term } = await createTermEnrollmentOpen();
          const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
          const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
          const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
  
          // Create and publish a course
          const courseData: CreateCoursePayload = {
              code: 'CS101',
              title: 'Intro to CS',
              credits: 3,
              capacity: 1,
              delivery_mode: DeliveryMode.IN_PERSON,
              location: 'Room 101'
          };
          const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
          const course = getSuccessData(courseResp);
          await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
  
          // Student enrolls
          const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
          const enrollment = getSuccessData(enrollResp);
  
          // Close and conclude the term
          let termResp = await apiClient.getTerm(term.id, registrarHeaders);
          let termData = getSuccessData(termResp);
          await apiClient.closeTermRegistration(term.id, { revision: termData.revision }, registrarHeaders);
          termResp = await apiClient.getTerm(term.id, registrarHeaders);
          termData = getSuccessData(termResp);
          await apiClient.concludeTerm(term.id, { revision: termData.revision }, registrarHeaders);
  
          // Registrar attempts to drop the student (enrollment is now COMPLETED after term conclusion)
          const dropResp = await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision }, registrarHeaders);
          expect(dropResp.status).toBe(409);
          const error = dropResp.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_ENROLLMENT_WRONG_STATE);
      });
  });
  
  describe('TC-GAP-09: Penalty fee applied exactly once on the third self-initiated drop', () => {
      it('should apply penalty fee on the 3rd self-initiated drop', async () => {
          const { term } = await createTermEnrollmentOpen();
          // Use unique IDs for this test
          const uniqueStudentId = 'b4444444-5555-6666-7777-888888888888';
          const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
          const uniqueProfessorId = 'a4444444-5555-6666-7777-888888888888';
          const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);
  
          // Student self-enrolls in three separate 1-credit courses.
          const courses = [];
          for (let i = 0; i < 3; i++) {
              const courseData: CreateCoursePayload = {
                  code: `GP${i + 1}${String(i + 10).padStart(2, '0')}`,
                  title: `Gap Test Penalty Course ${i + 1}`,
                  credits: 1,
                  capacity: 10,
                  delivery_mode: DeliveryMode.IN_PERSON,
                  location: `Room ${700 + i}`
              };
              const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
              const course = getSuccessData(courseResp);
              await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
              courses.push(course);
          }
  
          // Student drops Course A (1st self-drop)
          let enrollResp = await apiClient.createEnrollment(term.id, courses[0].id, {}, studentHeaders);
          let enrollment = getSuccessData(enrollResp);
          await apiClient.dropEnrollment(term.id, courses[0].id, enrollment.id, { revision: enrollment.revision }, studentHeaders);
  
          // Professor drops the student from Course B (admin drop)
          enrollResp = await apiClient.createEnrollment(term.id, courses[1].id, {}, studentHeaders);
          enrollment = getSuccessData(enrollResp);
          await apiClient.dropEnrollment(term.id, courses[1].id, enrollment.id, { revision: enrollment.revision }, professorHeaders);
  
          // Student drops Course C (2nd self-drop)
          enrollResp = await apiClient.createEnrollment(term.id, courses[2].id, {}, studentHeaders);
          enrollment = getSuccessData(enrollResp);
          await apiClient.dropEnrollment(term.id, courses[2].id, enrollment.id, { revision: enrollment.revision }, studentHeaders);
  
          // Student enrolls & drops Course D (now 3rd self-drop)
          const courseData: CreateCoursePayload = {
              code: 'GP901',
              title: 'Gap Test Penalty Course 4',
              credits: 1,
              capacity: 10,
              delivery_mode: DeliveryMode.ONLINE,
              online_link: 'https://example.com/gap9401'
          };
          const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
          const course = getSuccessData(courseResp);
          await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
          enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
          enrollment = getSuccessData(enrollResp);
          const dropResp = await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision }, studentHeaders);
          expect(dropResp.status).toBe(200);
      });
  });
  
  describe('TC-GAP-10: Course cancellation must refund students even if their balance is below the course cost', () => {
      it('should refund the student up to the amount they paid when a course is cancelled', async () => {
          const { term } = await createTermEnrollmentOpen();
          // Use unique IDs for this test
          const uniqueStudentId = 'b5555555-6666-7777-8888-999999999999';
          const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
          const uniqueProfessorId = 'a5555555-6666-7777-8888-999999999999';
          const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);
  
          // Create and publish a 4-credit course
          const courseData: CreateCoursePayload = {
              code: 'GAP101',
              title: 'Gap Test Advanced Finance',
              credits: 4,
              capacity: 10,
              delivery_mode: DeliveryMode.ONLINE,
              online_link: 'https://example.com/gap1001'
          };
          const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
          const course = getSuccessData(courseResp);
          await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
  
          // Fetch the updated course to get the correct revision after publishing
          const updatedCourseResp = await apiClient.getCourse(term.id, course.id, professorHeaders);
          const updatedCourse = getSuccessData(updatedCourseResp);
  
          // Student enrolls (balance becomes 40000)
          await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
  
          // Student pays 39000 (balance becomes 1000)
          await apiClient.makePayment(term.id, uniqueStudentId, { amount: 39000 }, studentHeaders);
  
          // Professor cancels the course
          await apiClient.cancelCourse(term.id, course.id, { revision: updatedCourse.revision }, professorHeaders);
  
          const paymentResp = await apiClient.makePayment(term.id, uniqueStudentId, { amount: 1 }, studentHeaders);
          expect(paymentResp.status).toBe(422);
          const error = paymentResp.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_OVERPAY_NOT_ALLOWED);
      });
  });
  
  describe('TC-GAP-11: After cancelling one of five active courses, professor may create a new one', () => {
      it('should allow professor to create a new course after cancelling one of their 5 courses', async () => {
          const { term } = await createTermEnrollmentOpen();
          // Use a unique professor for this test
          const uniqueProfessorId = 'c1111111-2222-3333-4444-666666666666';
          const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);
          const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
  
          // First, register the professor by having them make an API call
          // This ensures they're in the storage.users map
          await apiClient.listCourses(term.id, {}, professorHeaders);
  
          // Now registrar creates 5 courses for this professor
          for (let i = 0; i < 5; i++) {
              const courseData: CreateCoursePayload = {
                  code: `GPD${i + 1}${String(i + 1).padStart(2, '0')}`,
                  title: `Gap Test Course ${i + 1}`,
                  credits: 3,
                  capacity: 10,
                  delivery_mode: DeliveryMode.IN_PERSON,
                  location: `Room ${101 + i}`,
                  professor_id: uniqueProfessorId
              };
              const courseResp = await apiClient.createCourse(term.id, courseData, registrarHeaders);
              expect(courseResp.status).toBe(201);
          }
  
          // Cancel one course
          const coursesResp = await apiClient.listCourses(term.id, {}, professorHeaders);
          const courses = getSuccessData(coursesResp);
          const courseToCancel = courses[0];
          await apiClient.cancelCourse(term.id, courseToCancel.id, { revision: courseToCancel.revision }, professorHeaders);
  
          // Professor attempts to create a new course
          const newCourseData: CreateCoursePayload = {
              code: 'GPD601',
              title: 'Gap Test New Course',
              credits: 3,
              capacity: 10,
              delivery_mode: DeliveryMode.ONLINE,
              online_link: 'https://example.com/gap4new'
          };
          const newCourseResp = await apiClient.createCourse(term.id, newCourseData, professorHeaders);
          expect(newCourseResp.status).toBe(201);
      });
  });
  
  describe('TC-GAP-12: Payment endpoint must reject extraneous fields', () => {
      it('should reject payment with unknown fields', async () => {
          const { term } = await createTermEnrollmentOpen();
          const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
  
          const paymentResp = await apiClient.makePayment(term.id, TEST_USERS.STUDENT_A.id, { amount: 5000, hack: 'oops' } as any, studentHeaders);
          expect(paymentResp.status).toBe(400);
          const error = paymentResp.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_UNKNOWN_FIELD);
      });
  });
  
  describe('TC-GAP-13: Enrollment drop endpoint must reject unknown body members', () => {
      it('should reject drop with unknown fields', async () => {
          const { term } = await createTermEnrollmentOpen();
          const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
          const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

          // Create and publish a course
          const courseData: CreateCoursePayload = {
              code: 'SEC101',
              title: 'Security',
              credits: 3,
              capacity: 10,
              delivery_mode: DeliveryMode.IN_PERSON,
              location: 'Room 101'
          };
          const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
          const course = getSuccessData(courseResp);
          await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

          // Student enrolls
          const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
          const enrollment = getSuccessData(enrollResp);

          // Student attempts to drop with an unknown field
          const dropResp = await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision, extra: true } as any, studentHeaders);
          expect(dropResp.status).toBe(400);
          const error = dropResp.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_UNKNOWN_FIELD);
      });
  });

  describe('TC-GAP-14: Term state transition endpoints must reject unknown fields', () => {
      it('should reject open-registration with unknown fields', async () => {
          const { term } = await createTermPlanning();
          const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

          // Attempt to open with unknown field
          const response = await apiClient.openTermRegistration(
              term.id,
              { revision: term.revision, extraField: 'invalid' } as any,
              registrarHeaders
          );

          expect(response.status).toBe(400);
          const error = response.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_UNKNOWN_FIELD);
      });

      it('should reject close-registration with unknown fields', async () => {
          const { term } = await createTermEnrollmentOpen();
          const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

          // Attempt to close with unknown field
          const response = await apiClient.closeTermRegistration(
              term.id,
              { revision: term.revision, unknownProp: 123 } as any,
              registrarHeaders
          );

          expect(response.status).toBe(400);
          const error = response.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_UNKNOWN_FIELD);
      });

      it('should reject conclude with unknown fields', async () => {
          const { term } = await createTermEnrollmentClosed();
          const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

          // Attempt to conclude with unknown field
          const response = await apiClient.concludeTerm(
              term.id,
              { revision: term.revision, badField: true } as any,
              registrarHeaders
          );

          expect(response.status).toBe(400);
          const error = response.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_UNKNOWN_FIELD);
      });
  });

  describe('TC-GAP-15: Term state transitions must increment revision by 1', () => {
      it('should increment revision by 1 after successful open-registration', async () => {
          const { term } = await createTermPlanning();
          const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
          const initialRevision = term.revision;

          // Open registration
          const response = await apiClient.openTermRegistration(
              term.id,
              { revision: initialRevision },
              registrarHeaders
          );

          expect(response.status).toBe(200);
          const updatedTerm = getSuccessData(response);
          expect(updatedTerm.revision).toBe(initialRevision + 1);
          expect(updatedTerm.state).toBe(TermState.ENROLLMENT_OPEN);
      });

      it('should increment revision by 1 after successful close-registration', async () => {
          const { term } = await createTermEnrollmentOpen();
          const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
          const initialRevision = term.revision;

          // Close registration
          const response = await apiClient.closeTermRegistration(
              term.id,
              { revision: initialRevision },
              registrarHeaders
          );

          expect(response.status).toBe(200);
          const updatedTerm = getSuccessData(response);
          expect(updatedTerm.revision).toBe(initialRevision + 1);
          expect(updatedTerm.state).toBe(TermState.ENROLLMENT_CLOSED);
      });

      it('should increment revision by 1 after successful conclude', async () => {
          const { term } = await createTermEnrollmentClosed();
          const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
          const initialRevision = term.revision;

          // Conclude term
          const response = await apiClient.concludeTerm(
              term.id,
              { revision: initialRevision },
              registrarHeaders
          );

          expect(response.status).toBe(200);
          const updatedTerm = getSuccessData(response);
          expect(updatedTerm.revision).toBe(initialRevision + 1);
          expect(updatedTerm.state).toBe(TermState.CONCLUDED);
      });
  });

  describe('TC-GAP-16: Term conclusion must complete DRAFT courses', () => {
      it('should transition DRAFT courses to COMPLETED when concluding term', async () => {
          const { term } = await createTermEnrollmentOpen();
          const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
          const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

          // Create a DRAFT course (don't publish it)
          const courseData: CreateCoursePayload = {
              code: 'DRFT101',
              title: 'Draft Course',
              credits: 3,
              capacity: 10,
              delivery_mode: DeliveryMode.IN_PERSON,
              location: 'Room 101'
          };
          const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
          const draftCourse = getSuccessData(courseResp);
          expect(draftCourse.state).toBe(CourseState.DRAFT);

          // Close registration
          const closeResp = await apiClient.closeTermRegistration(
              term.id,
              { revision: term.revision },
              registrarHeaders
          );
          const closedTerm = getSuccessData(closeResp);

          // Conclude the term
          const concludeResp = await apiClient.concludeTerm(
              closedTerm.id,
              { revision: closedTerm.revision },
              registrarHeaders
          );
          expect(concludeResp.status).toBe(200);
          const concludedTerm = getSuccessData(concludeResp);
          expect(concludedTerm.state).toBe(TermState.CONCLUDED);

          // Verify DRAFT course is now COMPLETED
          const courseCheckResp = await apiClient.getCourse(term.id, draftCourse.id, registrarHeaders);
          const completedCourse = getSuccessData(courseCheckResp);
          expect(completedCourse.state).toBe(CourseState.COMPLETED);
      });
  });

  describe('TC-GAP-14: Waitlist promotion credit limit bypass', () => {
    it('should NOT promote a waitlisted student if it would exceed their credit limit', async () => {
      const { term } = await createTermEnrollmentOpen();
      
      // Use unique IDs for this test
      const student = 'b6666666-7777-8888-9999-aaaaaaaaaaaa';
      const studentHeaders = createHeaders(student, UserRole.STUDENT);
      const enrolledStudent = 'b7777777-8888-9999-aaaa-bbbbbbbbbbbb';
      const enrolledStudentHeaders = createHeaders(enrolledStudent, UserRole.STUDENT);
      // Use multiple professors to avoid hitting the 5-course limit
      const professor1 = 'a6666666-7777-8888-9999-aaaaaaaaaaaa';
      const professor1Headers = createHeaders(professor1, UserRole.PROFESSOR);
      const professor2 = 'a6666666-7777-8888-9999-bbbbbbbbbbbb';
      const professor2Headers = createHeaders(professor2, UserRole.PROFESSOR);
      
      // Create courses totaling 16 credits for the student
      const courses = [];
      for (let i = 0; i < 4; i++) {
        const credits = 4; // 4+4+4+4=16
        // Alternate between professors to avoid hitting the limit
        const professorHeaders = i < 2 ? professor1Headers : professor2Headers;
        const courseData: CreateCoursePayload = {
          code: `WL${i+1}${String(i+1).padStart(2, '0')}`,
          title: `Waitlist Test Course ${i+1}`,
          credits: credits,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${800+i}`
        };
        const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
        const course = getSuccessData(courseResp);
        await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
        courses.push(course);
      }
      
      // Enroll student in 16 credits worth of courses
      for (const course of courses) {
        await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      }
      
      // Create the full course that will trigger the promotion
      const fullCourseData: CreateCoursePayload = {
        code: 'WL501',
        title: 'Waitlist Test Full Course',
        credits: 3,
        capacity: 1,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/wl501'
      };
      const fullCourseResp = await apiClient.createCourse(term.id, fullCourseData, professor2Headers);
      const fullCourse = getSuccessData(fullCourseResp);
      await apiClient.publishCourse(term.id, fullCourse.id, { revision: fullCourse.revision }, professor2Headers);
      
      // Another student takes the only seat
      const enrollment1Resp = await apiClient.createEnrollment(term.id, fullCourse.id, {}, enrolledStudentHeaders);
      const enrollment1 = getSuccessData(enrollment1Resp);
      expect(enrollment1.state).toBe(EnrollmentState.ENROLLED);
      
      // Use registrar to add our target student to waitlist (bypasses credit limit check)
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const enrollment2Resp = await apiClient.createEnrollment(
        term.id, 
        fullCourse.id, 
        { student_id: student }, 
        registrarHeaders
      );
      const enrollment2 = getSuccessData(enrollment2Resp);
      expect(enrollment2.state).toBe(EnrollmentState.WAITLISTED);
      
      // Enrolled student drops, freeing a seat
      await apiClient.dropEnrollment(term.id, fullCourse.id, enrollment1.id, { revision: enrollment1.revision }, enrolledStudentHeaders);
      
      // Check if the waitlisted student was promoted
      const updatedEnrollmentResp = await apiClient.getEnrollment(term.id, fullCourse.id, enrollment2.id, studentHeaders);
      const updatedEnrollment = getSuccessData(updatedEnrollmentResp);
      
      // The student should remain waitlisted because 16 + 3 = 19 > 18 (credit limit)
      expect(updatedEnrollment.state).toBe(EnrollmentState.WAITLISTED);
      
      // The course should have 1 available seat
      const courseDetailsResp = await apiClient.getCourse(term.id, fullCourse.id, professor2Headers);
      const courseDetails = getSuccessData(courseDetailsResp);
      expect(courseDetails.available_seats).toBe(1);
    });
  });

  describe('TC-GAP-14B: Waitlist promotion skips over-credit students', () => {
    it('should skip waitlisted students who would exceed credit limit and promote the next eligible', async () => {
      const { term } = await createTermEnrollmentOpen();
      
      // Use unique IDs for this test
      const student1 = 'ba111111-2222-3333-4444-555555555555'; // Will be at credit limit
      const student1Headers = createHeaders(student1, UserRole.STUDENT);
      const student2 = 'ba222222-3333-4444-5555-666666666666'; // Will have room for more credits
      const student2Headers = createHeaders(student2, UserRole.STUDENT);
      const enrolledStudent = 'ba333333-4444-5555-6666-777777777777';
      const enrolledStudentHeaders = createHeaders(enrolledStudent, UserRole.STUDENT);
      // Use multiple professors to avoid hitting the 5-course limit
      const professor1 = 'aa111111-2222-3333-4444-555555555555';
      const professor1Headers = createHeaders(professor1, UserRole.PROFESSOR);
      const professor2 = 'aa222222-3333-4444-5555-666666666666';
      const professor2Headers = createHeaders(professor2, UserRole.PROFESSOR);
      const professor3 = 'aa333333-4444-5555-6666-777777777777';
      const professor3Headers = createHeaders(professor3, UserRole.PROFESSOR);
      
      // Enroll student1 in 16 credits (using multiple courses to reach exactly 16)
      for (let i = 0; i < 4; i++) {
        const credits = 4; // 4+4+4+4=16
        // Use professor1 for first 2 courses, professor2 for next 2
        const professorHeaders = i < 2 ? professor1Headers : professor2Headers;
        const courseData: CreateCoursePayload = {
          code: `MX${i+1}${String(i+1).padStart(2, '0')}`,
          title: `Max Credit Course ${i+1}`,
          credits: credits,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${860+i}`
        };
        const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
        const course = getSuccessData(courseResp);
        await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
        await apiClient.createEnrollment(term.id, course.id, {}, student1Headers);
      }
      
      // Enroll student2 in only 12 credits
      for (let i = 0; i < 3; i++) {
        // Use professor3 for all 3 courses
        const courseData: CreateCoursePayload = {
          code: `LT${i+1}${String(i+1).padStart(2, '0')}`,
          title: `Light Credit Course ${i+1}`,
          credits: 4,
          capacity: 10,
          delivery_mode: DeliveryMode.ONLINE,
          online_link: `https://example.com/lt${i+1}`
        };
        const courseResp = await apiClient.createCourse(term.id, courseData, professor3Headers);
        const course = getSuccessData(courseResp);
        await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professor3Headers);
        await apiClient.createEnrollment(term.id, course.id, {}, student2Headers);
      }
      
      // Create the contested 3-credit course with only 1 seat
      const contestedCourseData: CreateCoursePayload = {
        code: 'CNT101',
        title: 'Contested Course',
        credits: 3,
        capacity: 1,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/cnt101'
      };
      const contestedCourseResp = await apiClient.createCourse(term.id, contestedCourseData, professor3Headers);
      const contestedCourse = getSuccessData(contestedCourseResp);
      await apiClient.publishCourse(term.id, contestedCourse.id, { revision: contestedCourse.revision }, professor3Headers);
      
      // Enrolled student takes the seat
      const enrollment0Resp = await apiClient.createEnrollment(term.id, contestedCourse.id, {}, enrolledStudentHeaders);
      const enrollment0 = getSuccessData(enrollment0Resp);
      expect(enrollment0.state).toBe(EnrollmentState.ENROLLED);
      
      // Use registrar to add both students to waitlist (bypasses credit limit check for student1)
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      // Student1 joins waitlist first (16 + 3 = 19 > 18, so cannot be promoted)
      const enrollment1Resp = await apiClient.createEnrollment(
        term.id, 
        contestedCourse.id, 
        { student_id: student1 }, 
        registrarHeaders
      );
      const enrollment1 = getSuccessData(enrollment1Resp);
      expect(enrollment1.state).toBe(EnrollmentState.WAITLISTED);
      
      // Student2 joins waitlist second (12 + 3 = 15 < 18, so can be promoted)
      const enrollment2Resp = await apiClient.createEnrollment(term.id, contestedCourse.id, {}, student2Headers);
      const enrollment2 = getSuccessData(enrollment2Resp);
      expect(enrollment2.state).toBe(EnrollmentState.WAITLISTED);
      
      // Enrolled student drops, freeing a seat
      await apiClient.dropEnrollment(term.id, contestedCourse.id, enrollment0.id, { revision: enrollment0.revision }, enrolledStudentHeaders);
      
      // Check enrollments - student1 should still be waitlisted, student2 should be promoted
      const updatedEnrollment1Resp = await apiClient.getEnrollment(term.id, contestedCourse.id, enrollment1.id, student1Headers);
      const updatedEnrollment1 = getSuccessData(updatedEnrollment1Resp);
      expect(updatedEnrollment1.state).toBe(EnrollmentState.WAITLISTED);
      
      const updatedEnrollment2Resp = await apiClient.getEnrollment(term.id, contestedCourse.id, enrollment2.id, student2Headers);
      const updatedEnrollment2 = getSuccessData(updatedEnrollment2Resp);
      expect(updatedEnrollment2.state).toBe(EnrollmentState.ENROLLED);
    });
  });

  describe('TC-GAP-15: Incomplete tuition refund for cancelled course', () => {
    it('should expose that refunds are capped at current balance instead of full course cost', async () => {
      const { term } = await createTermEnrollmentOpen();
      
      // Use unique IDs
      const student = 'b8888888-9999-aaaa-bbbb-cccccccccccc';
      const studentHeaders = createHeaders(student, UserRole.STUDENT);
      const professor = 'a8888888-9999-aaaa-bbbb-cccccccccccc';
      const professorHeaders = createHeaders(professor, UserRole.PROFESSOR);
      
      const courseData: CreateCoursePayload = {
        code: 'REF101',
        title: 'Refund Test Course',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room 900',
        professor_id: professor
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
      
      // Get updated course revision after publish
      const updatedCourseResp = await apiClient.getCourse(term.id, course.id, professorHeaders);
      const updatedCourse = getSuccessData(updatedCourseResp);
      
      // Student enrolls (balance becomes 30000)
      await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      
      // Make partial payment of 10000 cents
      await apiClient.makePayment(term.id, student, { amount: 10000 }, studentHeaders);
      
      // Professor cancels the course
      await apiClient.cancelCourse(term.id, course.id, { revision: updatedCourse.revision }, professorHeaders);
      
      // According to spec, balance should be 20000 - 30000 = -10000
      // But implementation will result in balance of 0
      // Try to pay $1 - if balance is 0, this will fail with OVERPAY_NOT_ALLOWED
      const paymentResp = await apiClient.makePayment(term.id, student, { amount: 1 }, studentHeaders);
      expect(paymentResp.status).toBe(422);
      const error = paymentResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_OVERPAY_NOT_ALLOWED);
    });
  });

  describe('TC-GAP-16: Administrative drop after registration closed', () => {
    it('should fail when professor tries to drop student after registration is closed', async () => {
      const { term } = await createTermEnrollmentOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      
      // Use unique IDs to avoid conflicts with TC-GAP-07
      const student = 'b9999999-aaaa-bbbb-cccc-dddddddddddd';
      const studentHeaders = createHeaders(student, UserRole.STUDENT);
      const professor = 'a9999999-aaaa-bbbb-cccc-dddddddddddd';
      const professorHeaders = createHeaders(professor, UserRole.PROFESSOR);
      
      const courseData: CreateCoursePayload = {
        code: 'ADM101',
        title: 'Admin Drop Test',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room 950',
        professor_id: professor
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
      
      // Student enrolls
      const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      const enrollment = getSuccessData(enrollResp);
      
      // Close registration
      const termResp = await apiClient.getTerm(term.id, registrarHeaders);
      const termData = getSuccessData(termResp);
      await apiClient.closeTermRegistration(term.id, { revision: termData.revision }, registrarHeaders);
      
      // Professor attempts to drop the student
      const dropResp = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        professorHeaders
      );
      
      // According to spec, this should fail
      expect(dropResp.status).toBe(409);
      const error = dropResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_REGISTRATION_CLOSED);
    });
  });
});
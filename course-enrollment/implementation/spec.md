# Overview

The University Course Registration & Enrollment API is a container-scoped system for managing academic course offerings and student enrollments within an **AcademicTerm**. All data is strictly isolated to a specific term; every course and enrollment exists only within one AcademicTerm. The API coordinates interactions between **Students**, **Professors**, and a **Registrar**, enforcing role-based access control (RBAC) at both the endpoint and field level. It supports complex academic workflows like course publishing, student enrollment (including waitlisting), course cancellation, and term conclusion, all while maintaining audit trails and system invariants.

This design emphasizes **high-ROI complexity**. Business rules are **interactive and distributed** – actions on one resource often trigger side effects on others. For example, dropping a course may quietly free a seat that automatically enrolls a waitlisted student. Independent state machines (for course lifecycle and enrollment status) interlock: a course’s state can gate what actions are allowed on its enrollments and vice versa. The API uses **container-scoped ledgers** to track critical counters like available seats in each course and tuition balances for each student, affecting behavior across the system. There are no time-based triggers (e.g. no date-based auto-closures); all changes result from explicit endpoints or state-driven events, ensuring determinism for testing.

The specification is rich in **pedantic validations** and conditional requirements to create a “minefield” of edge cases. Each request is rigorously validated for format, business rules, and system invariants. Every distinct failure mode yields a specific error code for precision. GET endpoints are not passive data fetches – they return context-dependent views: fields may be redacted or derived based on the requesting user’s role and the resource’s state. The interplay of roles, state machines, ledgers, and rules leads to **emergent behaviors**. For instance, multiple independent rules (credit limits, seat availability, term status) can combine to produce complex outcomes, resolved by a defined hierarchy of precedence. The overall goal is a logically complete API that is challenging to implement correctly without a holistic understanding of the system.

# User Roles

**Student:** Represents a learner who can search and enroll in courses within a term. Students are permitted to view course listings and details (with some information hidden, such as other students in the course). They can create enrollment records for themselves (register for a course) and drop their own enrollments. Students cannot create or modify courses, and cannot see administrative fields like financial ledgers of others. Certain endpoints (like paying tuition) are available to students but only for their own account. All student actions are constrained by business rules (e.g. cannot exceed credit limits, cannot enroll in closed courses, etc.).

**Professor:** Represents an instructor who teaches courses. Professors can create courses (which by default list themselves as the instructor) and manage the lifecycle of courses they own. This includes publishing a draft course to open it for enrollment and potentially canceling their course. Professors can view the details of their own courses including full enrollment rosters and related ledger info (like available seats). They **cannot** enroll students (except via the Registrar role) or modify courses they do not teach. When retrieving data, professors see more details for their courses (e.g. list of enrolled student IDs) that students would not see. Professors do not have direct access to student tuition ledgers or other courses they don’t instruct.

**Registrar:** Represents an administrative user with broad privileges. The Registrar can manage academic terms and oversee all courses and enrollments within a term. They can create a new AcademicTerm and are the only role allowed to change the state of a term (e.g. opening or closing registration, concluding the term). Registrars can create courses (assigning any professor as instructor), publish or cancel any course, and override certain rules when necessary (e.g. bypass credit limits for special enrollments). They can also enroll or drop students on behalf of students (administrative overrides), and process tuition payments. The Registrar has full read access to all information: e.g. they can retrieve any course with full details (including rosters and ledger statuses) and view any student’s enrollment records or ledger balance. If a rule would normally prevent an action (like credit overload), a Registrar’s explicit action can supersede it where noted by the business rules.

*RBAC Enforcement:* Each endpoint specifies allowed roles and often further requires that the acting user is related to the resource (e.g. a Professor must be the instructor of that course to modify it). Unauthorized role access is rejected with specific errors. Additionally, field-level visibility is enforced on responses: sensitive fields appear only for permitted roles (for example, only a course’s instructor or Registrar sees the full enrollment list of a course). This ensures each user role experiences a properly scoped view of the system.

# Resources

## **AcademicTerm** (container resource)

Top-level container that scopes a specific academic term/semester. All courses and enrollments belong to one AcademicTerm.

* **id** (string, required, readonly, pattern: "^[0-9a-fA-F\\-]{36}$"): The unique identifier of the term (UUID).
* **name** (string, required, unique): Human-readable term name (e.g. "Fall 2025"). Used for display; uniqueness ensures no duplicate term names.
* **state** (string, enum: `['PLANNING','ENROLLMENT_OPEN','ENROLLMENT_CLOSED','CONCLUDED']`, default: `'PLANNING'`): Lifecycle status of the term. Controls global behavior:

  * **PLANNING:** Term exists but registration not yet open. Courses can be created/edited, but students cannot enroll.
  * **ENROLLMENT_OPEN:** Registration period is active. Students may enroll/drop freely (within other constraints).
  * **ENROLLMENT_CLOSED:** Registration is closed (classes in session). No new enrollments or drops by students are allowed (Registrar overrides possible).
  * **CONCLUDED:** Term is finished. No further changes to courses or enrollments (all courses automatically marked completed, remaining enrollments finalized).
* **created_by** (string, required): FK of User (Registrar) who created the term.
* **created_at** (string, required, readonly, pattern: "^\d{4}-\d{2}-\d{2}T"): ISO-8601 timestamp when the term was created.
* **revision** (integer, required, min: 0): Revision number for optimistic locking on term updates (e.g. state changes).

## **Course**

Represents a course offered in a specific term. Courses are contained within an AcademicTerm.

* **id** (string, required, readonly, pattern: "^[0-9a-fA-F\\-]{36}$"): Unique course identifier (UUID).
* **term_id** (string, required): FK to AcademicTerm.id. Every course belongs to exactly one term.
* **code** (string, required, pattern: "^[A-Z]{2,4}[0-9]{3}$"): Course code (e.g. "CS101"). Must be 2–4 uppercase letters followed by 3 digits. **Unique per term**.
* **title** (string, required, minLength: 1, maxLength: 100): Course title (e.g. "Introduction to Computer Science"). Cannot be empty. `ERR_INVALID_FIELD_LENGTH` for violations.
* **description** (string, optional, maxLength: 1000): Detailed course description.
* **credits** (integer, required, min: 1, max: 5): Credit hours awarded for completing the course (1–5 range).
* **capacity** (integer, required, min: 1, max: 500): Maximum number of students that can be enrolled (not counting waitlisted). Must be at least 1. Once set, capacity can only be increased via an update (not decreased below current enrollment count).
* **professor_id** (string, required): FK User.id of the instructor.

  * If created by a Professor, this **must** be that professor’s own ID (the system will default it to `auth.id` if not provided by a professor). Professors cannot create courses for other instructors.
  * If created by the Registrar, a valid professor_id must be provided (assigning an instructor to the course).
* **state** (string, enum: `['DRAFT','OPEN','IN_PROGRESS','COMPLETED','CANCELLED']`, default: `'DRAFT'`): Workflow state of the course (see **CourseLifecycle** state machine below).

  * **DRAFT:** Initial state when created. Course is not yet published; students cannot view or enroll.
  * **OPEN:** Course is published and open for student enrollment (if the term is also ENROLLMENT_OPEN).
  * **IN_PROGRESS:** Course has begun (enrollment period closed). No new enrollments via student action (drops may or may not be allowed).
  * **COMPLETED:** Course has finished normally.
  * **CANCELLED:** Course was cancelled and will not run.
* **enrolled_count** (integer, readonly): DERIVED – number of students currently enrolled (state = ENROLLED) in this course. Updated whenever students enroll or drop. (See **Derivations**)
* **waitlist_count** (integer, readonly): DERIVED – number of students currently waitlisted (enrollment state = WAITLISTED) for this course.
* **available_seats** (integer, readonly): DERIVED – remaining seats available for enrollment. Typically `capacity - enrolled_count`. Will be 0 if course is full or not open.
* **created_at** (string, required, readonly): ISO-8601 timestamp when the course was created.
* **updated_at** (string, required, readonly): ISO-8601 timestamp of last update to the course (e.g. when state or capacity changed).
* **revision** (integer, required, min: 0): Revision number for optimistic concurrency. Must match on update actions (publishing, cancelling, etc.) to avoid conflicting modifications.

## **Enrollment**

Represents a student’s enrollment (or attempted enrollment) in a course. Enrollments are contained within an AcademicTerm and associated to a specific Course.

* **id** (string, required, readonly, pattern: "^[0-9a-fA-F\\-]{36}$"): Unique enrollment identifier (UUID).
* **term_id** (string, required): FK AcademicTerm.id (should match the course’s term).
* **course_id** (string, required): FK Course.id. The course the student is (or wishes to be) enrolled in.
* **student_id** (string, required): FK User.id of the student.
* **state** (string, enum: `['ENROLLED','WAITLISTED','DROPPED','COMPLETED']`, default: `'ENROLLED'` upon successful enrollment): Status of this enrollment (see **EnrollmentStatus** state machine).

  * **ENROLLED:** The student is actively enrolled in the course (counts toward capacity).
  * **WAITLISTED:** The student is on the waitlist (no seat yet, not counted in capacity or tuition until promoted).
  * **DROPPED:** The enrollment was cancelled by the student or removed (student will no longer attend; if this was an ENROLLED student, their seat is freed; if they were waitlisted, they are removed from waitlist).
  * **COMPLETED:** The student completed the course (earned credit). This state is set when a course concludes for all students who remained enrolled through the end.
* **grade** (string, readonly, optional): Grade or result achieved (e.g. "A", "B", "PASS"). This is only set at completion and only visible to the student and Registrar once the course is **COMPLETED**. (Professors can see grades they assigned to their students; students see only their own grade.)
* **created_at** (string, required, readonly): ISO-8601 timestamp when the enrollment record was created (when the student attempted to enroll). Used to order waitlists (FIFO).
* **revision** (integer, required, min: 0): Revision for concurrency. This must match when making updates to the enrollment (e.g. certain drop or grade update operations), to ensure no interim changes.

**Data Isolation:** All resource IDs are globally unique UUIDs, but the system *always* validates that `term_id` in the path matches the resource’s actual `term_id`. The nesting in endpoints (`/terms/{termId}/courses/{courseId}/...`) is enforced in logic: if a resource does not belong to the specified term (or a sub-resource to the specified parent), the request fails with a not-found error. This prevents any cross-term data access or misrouting (e.g., one cannot fetch a course from the wrong term or an enrollment from the wrong course). Additionally, if a resource ID is malformed (not a valid UUID) or simply doesn’t exist, a `404 Not Found` error is returned with the appropriate error code (e.g. `ERR_TERM_NOT_FOUND`, `ERR_COURSE_NOT_FOUND`).

# State Machines

The system defines formal state machines to manage transitions and guard conditions for key workflows. Each state machine has well-defined states and allowed transitions (often triggered by specific events or actions). State transitions often have side effects and may be subject to validation rules.

## **CourseLifecycle** – States of `Course.state`

States: **DRAFT**, **OPEN**, **IN_PROGRESS**, **COMPLETED**, **CANCELLED**

* **DRAFT:** Initial state for a new course. Only the professor (instructor) or Registrar can see and modify a DRAFT course. Students cannot view or enroll in DRAFT courses (attempts yield not found).
* **OPEN:** The course is published and open for enrollment. Transitioned from DRAFT via an explicit publish action by the instructor or Registrar. Students can see OPEN courses (if the term is ENROLLMENT_OPEN) and may attempt to enroll.
* **IN_PROGRESS:** The course has started (enrollment is closed). Typically, when the term’s registration period ends, all OPEN courses become IN_PROGRESS. No new student enrollments (except possible administrative overrides) are allowed, though drops might or might not be allowed depending on policy. This state can be triggered by the Registrar closing the term’s registration or manually via an endpoint if needed.
* **COMPLETED:** The course has concluded successfully. Usually set when the academic term is concluded (all courses that were IN_PROGRESS move to COMPLETED). In this state, no further changes to enrollments are allowed except viewing records. A course in COMPLETED state can no longer be modified.
* **CANCELLED:** The course was cancelled and will not continue. A course can be cancelled from DRAFT, OPEN, or IN_PROGRESS by the instructor or Registrar (e.g. due to low enrollment or other reasons). Cancelling a course triggers automatic drops/refunds of all associated enrollments. Cancelled is a terminal state (cannot be reopened).

**Allowed Transitions:** (Triggers in parentheses correspond to events or explicit actions; see Business Rules for conditions)

* **DRAFT -> OPEN** *(on action: publish_course)*: Instructor or Registrar publishes the course. Only allowed if the course is in DRAFT and the AcademicTerm is in ENROLLMENT_OPEN state. After transition, students can see and enroll in the course.
* **OPEN -> IN_PROGRESS** *(on event: term_closed OR action: close_course_enrollment)*: When the term’s enrollment period closes, all OPEN courses move to IN_PROGRESS. This can happen via a Registrar action on the term (closing registration) or potentially a scheduled event (no time-based logic here, so it’s an explicit call). In-progress signifies no new enrollment through standard means.
* **IN_PROGRESS -> COMPLETED** *(on event: term_concluded)*: When the term is concluded, any course still in progress transitions to COMPLETED. All active enrollments move to completed as well.
* **OPEN -> CANCELLED** *(on action: cancel_course)*: Cancel a course during enrollment period.
* **IN_PROGRESS -> CANCELLED** *(on action: cancel_course)*: Cancel a course after it started (e.g. emergency cancellation).
* **DRAFT -> CANCELLED** *(on action: cancel_course)*: An unpublished course can be directly cancelled (essentially deleted) by the instructor or Registrar.
* **ANY -> CANCELLED**: (on event: forced_cancel) — The Registrar can force-cancel a course from any state if necessary (this covers DRAFT, OPEN, IN_PROGRESS as above; no effect if already completed or cancelled). Once cancelled, no further state changes occur.
* **OPEN -> COMPLETED**: *Not direct.* (A course cannot skip IN_PROGRESS; it must either go through IN_PROGRESS or be cancelled if it ends early. The normal path to completion if not explicitly closed earlier is OPEN -> IN_PROGRESS -> COMPLETED via term events.)

State transitions that are not listed above are *not allowed*. Any attempt to transition a course to a state that is not permitted (e.g. re-opening a Completed course, or publishing a course already Open) must be rejected with `ERR_ILLEGAL_COURSE_STATE_TRANSITION` or a more specific `ERR_COURSE_WRONG_STATE`.

## **EnrollmentStatus** – States of `Enrollment.state`

States: **ENROLLED**, **WAITLISTED**, **DROPPED**, **COMPLETED**

* **ENROLLED:** The student is actively enrolled with a seat in the course. This is the initial state for a successful enrollment when a seat is available. Enrollments in this state count against the course’s capacity and incur tuition charges.
* **WAITLISTED:** The enrollment request is waiting for a seat (the course was full at the time of enrollment). Waitlisted enrollments do *not* count towards course capacity and do not incur tuition until promoted. A waitlisted student can later be automatically moved to ENROLLED if a seat opens up (in which case tuition will be charged at that moment).
* **DROPPED:** The enrollment was cancelled or withdrawn *before* course completion. This can happen either by the student (withdrawing from the course), by the professor/Registrar removing the student, or automatically if the course is cancelled. If the student was ENROLLED, their seat is freed (and possibly given to a waitlisted student); if they were WAITLISTED, they are simply removed from the waitlist. Dropped enrollments do not count toward capacity or credit totals (and may trigger refunds or fee penalties as defined by rules).
* **COMPLETED:** The enrollment is finalized with the student having completed the course. Reaching this state typically means the student stayed enrolled through the end of the course (or term). Completed enrollments award credit to the student. Once an enrollment is in COMPLETED, the student cannot drop it (it’s in the past), and it remains as an academic record.

**Allowed Transitions:**

* **(Initial) -> ENROLLED:** Creation of an enrollment when a seat is available.
* **(Initial) -> WAITLISTED:** Creation of an enrollment when no seat is available (course at capacity). The enrollment stays in waitlist queue.
* **WAITLISTED -> ENROLLED** *(on event: seat_vacated)*: A seat became available (e.g. another student dropped or capacity increased), and the first student in line is automatically moved from WAITLISTED to ENROLLED. This transition triggers charging tuition for the now-enrolled student and sends a notification (via audit log) that they are enrolled.
* **ENROLLED -> DROPPED** *(on action: drop_enrollment)*: Student (or authorized user) drops the course while it’s in progress or during enrollment period. Also occurs if the course is cancelled (all ENROLLED become DROPPED as a result of cancellation, though we might mark that internally as a special case of drop).
* **WAITLISTED -> DROPPED** *(on action: drop_enrollment)*: Student (or admin) removes a waitlisted request (or if course is cancelled – all waitlists are effectively dropped).
* **ENROLLED -> COMPLETED** *(on event: course_completed)*: When the course reaches completion (normally at term conclusion), all enrollments still ENROLLED transition to COMPLETED.
* **WAITLISTED -> COMPLETED**: **Not allowed.** A student cannot complete a course directly from the waitlist; if a course ends and someone is still waitlisted, that enrollment will be treated as DROPPED (did not get a seat in time).
* **DROPPED -> ENROLLED**: **Not allowed.** (Once dropped, a new enrollment would require creating a new enrollment record; students must re-enroll if they dropped, subject to capacity.)
* **ANY -> DROPPED** *(on event: course_cancelled)*: If a course is cancelled, all its enrollments (ENROLLED or WAITLISTED) transition to DROPPED as part of the cancellation cascade. This ensures no enrollment remains active in a cancelled course.
* **ANY -> COMPLETED**: (N/A except ENROLLED as above; DROPPED or WAITLISTED will not go to completed.)

As with courses, any illegal transition attempt (e.g. dropping an already dropped enrollment, or trying to directly waitlist someone who is already enrolled) is prevented. In general, **state transitions are triggered by explicit endpoints or internal events** – there is no automatic time-based progression, ensuring testable deterministic flows.

# Ledgers

The API uses **container-scoped ledgers** to maintain counts and balances that are shared across resources within an AcademicTerm. Ledgers are special data records that support atomic credit/debit operations and ensure system-wide invariants (e.g., seats cannot go negative, tuition must balance out). All ledger operations are append-only and occur as part of endpoint actions or event rules, providing an audit trail of changes.

## **CourseSeatLedger** (Ledger: seats remaining per course)

**Scope:** AcademicTerm (one ledger entry per Course within the term).
Tracks the remaining available seats for each course, enforcing capacity constraints.

* **term_id** (string, ledger key part 1): The AcademicTerm container.
* **course_id** (string, ledger key part 2): Identifies the course within the term.
* **seats_available** (integer): The number of seats currently available in the course (non-negative). This is essentially a mirror of `Course.available_seats`. Initialized to `Course.capacity` when the course is opened.

  * **Debit** operation on this ledger **decreases** available seats (e.g. when a student enrolls, we debit 1 seat).
  * **Credit** operation **increases** available seats (e.g. when a student drops or a course’s capacity is increased, we credit seats back).
* **invariant:** `seats_available >= 0` and never exceeds the course’s capacity. The system prevents debiting beyond available seats or crediting above capacity (any attempt to do so fails with `ERR_CAPACITY_EXCEEDED` or `ERR_LEDGER_INVALID_OP`). The ledger thus ensures no more students enroll than the defined capacity unless capacity is explicitly raised.

*(Note: The CourseSeatLedger is implicitly updated through enrollment actions; there is no direct endpoint to manipulate seats outside of course capacity changes. This ledger provides a single source of truth for how many seats remain free and gates enrollment actions.)*

## **StudentTuitionLedger** (Ledger: tuition balance per student per term)

**Scope:** AcademicTerm (one ledger entry per Student in the term).
Tracks the outstanding tuition balance (or credits) for a student in a term, based on their course enrollments and payments.

* **term_id** (string, ledger key part 1): The AcademicTerm container.
* **student_id** (string, ledger key part 2): Identifies the student within the term.
* **balance_cents** (integer): The current balance (in cents) the student owes for that term’s courses. A positive balance means amount owed; zero means fully paid. (Negative balances are not allowed – overpayments beyond 0 will be rejected or kept at 0.)

  * When a student **enrolls** in a course, their balance is increased by the course’s tuition cost. (In this model, assume each credit hour corresponds to a fixed amount, e.g. $100 per credit. For simplicity, use a constant cost per credit – see below – rather than per course fees.)
  * When a student **drops** an enrolled course (within allowed period), their balance is decreased by the course’s cost (tuition refund).
  * When a **payment** is made (the student or Registrar posts a payment), the balance is decreased accordingly (crediting the student’s account).
  * If a **penalty fee** is applied (e.g. drop fee after threshold), the balance is increased (i.e. an additional charge).
* **invariant:** `balance_cents >= 0`. The ledger will not allow a debit that would take the balance below 0 (no overpayment – attempting to pay more than owed will fail with `ERR_OVERPAY_NOT_ALLOWED`). All credits/debits on this ledger are atomic to prevent race conditions on financial calculations.
* The ledger entry for a student is created on the first enrollment if not already present (initialized with 0 balance). All subsequent enrollments/payments use the same ledger record within that term.

*This ledger ensures financial tracking per student per term. It is container-scoped: a student has a separate balance in each term.* For example, if a student is enrolled in 3 credits at $100/credit, their balance_cents would be 30000 (assuming no payment yet). Paying $100 (10000 cents) would debit the ledger to 20000, etc.

## **Constants and Pricing Assumptions**

To avoid hard-coding magic numbers in rules, the following constants define global policy values:

* **COST_PER_CREDIT** = 10000 (cents). Each credit hour costs $100. So a 3-credit course costs 30000 cents.
* **MAX_CREDITS_PER_TERM** = 18. Students are not allowed to take more than 18 credits by default (unless overridden by Registrar).
* **MAX_COURSES_PER_PROF** = 5. A professor cannot be the instructor for more than 5 courses in one term (to prevent course overload).
* **MAX_DROP_COUNT_PER_TERM** = 3. A student may drop at most 3 courses in a term. On the 4th drop attempt, the action is blocked.
* **DROP_PENALTY_FEE** = 5000 (cents). Applied to a student’s tuition ledger on the 3rd drop (i.e., after exceeding 2 previous drops) as an escalating penalty.
* **FULL_TIME_CREDITS_THRESHOLD** = 12. For reference, 12+ credits is considered full-time (this might be used as a derived status, e.g., to determine if a student is full-time or part-time, though it doesn’t directly change rules in this spec except perhaps as informational).

# Error Catalogue

All errors are returned in a standardized format with an `error_id` and human-readable message (as per a global response schema). Each distinct validation or rule violation has a unique `error_id`. Below is a catalogue of possible error codes, organized by category, along with their associated HTTP response status:

#### **Authentication & Authorization Errors**

These occur when a user’s role or identity doesn’t permit an action.

* `ERR_UNAUTHORIZED_ROLE` – **403 Forbidden** – The authenticated user’s role is not permitted to call this endpoint or perform this action.
* `ERR_NOT_INSTRUCTOR` – **403 Forbidden** – The professor attempting an action is not the instructor of the target course (or the user is not the course owner where ownership is required).
* `ERR_PERMISSION_DENIED` – **403 Forbidden** – General permission error (used when none of the more specific codes apply). For example, a user tries to access a resource they don’t own or perform an admin-only operation.

#### **Resource & Not Found Errors**

These occur when referenced entities don’t exist or don’t belong in the given scope.

* `ERR_TERM_NOT_FOUND` – **404 Not Found** – The specified AcademicTerm does not exist (or is not accessible to the user). Also used if a resource is not under the given term (mismatched IDs yielding not found).
* `ERR_COURSE_NOT_FOUND` – **404 Not Found** – The specified Course was not found (either the ID is invalid, the course is in a different term than provided in path, or it’s a DRAFT that the user is not allowed to see – effectively making it invisible).
* `ERR_ENROLLMENT_NOT_FOUND` – **404 Not Found** – The specified Enrollment was not found (invalid ID or not associated with the given course/term, or not visible to the user).
* `ERR_STUDENT_NOT_FOUND` – **404 Not Found** – A student ID provided in a request (e.g. by Registrar to enroll someone, or pay on behalf) does not correspond to any known user with role Student (or not associated with this term).
* `ERR_TERM_CLOSED` – **404 Not Found** – Returned in contexts where an operation is not applicable because the term is concluded or closed for changes, making the target resource effectively unavailable (e.g. trying to enroll in a term that has ended yields this to indicate the term is not open).

*(Note: The system often uses 404 to avoid leaking information. For example, if a Student tries to access a course in DRAFT, the response is a 404 with `ERR_COURSE_NOT_FOUND` rather than a 403, so as not to reveal the course exists.)*

#### **Validation & Format Errors**

These errors indicate bad input data (violating format, missing fields, etc.). All such errors use **400 Bad Request** status.

* `ERR_INVALID_ID_FORMAT` – **400 Bad Request** – One of the provided IDs (term, course, enrollment, etc.) is not a valid UUID format. The request is rejected without looking up the resource.
* `ERR_INVALID_COURSE_CODE` – **400 Bad Request** – The course code in the request body fails validation (not matching the required pattern or not unique within the term).
* `ERR_INVALID_CREDITS` – **400 Bad Request** – The `credits` value is outside the allowed range or not provided when required.
* `ERR_INVALID_CAPACITY` – **400 Bad Request** – The `capacity` provided for a course is invalid (zero, negative, or above the maximum allowed, or less than current enrollment on update).
* `ERR_INVALID_ENUM_VALUE` – **400 Bad Request** – A provided value for an enum field (like state, role, etc.) is not one of the allowed options.
* `ERR_MISSING_REQUIRED_FIELD` – **400 Bad Request** – The request body is missing a required field. The error message will indicate which field is absent. (e.g. “professor_id is required when creating a course as Registrar”).
* `ERR_CONDITIONAL_FIELD_REQUIRED` – **400 Bad Request** – A required field based on another field’s value is missing. For example, if `delivery_mode` is "IN_PERSON" but `location` was not provided (or if `delivery_mode` is "ONLINE" but `online_link` missing).
* `ERR_FIELD_CONFLICT` – **400 Bad Request** – Two fields were provided that are mutually exclusive or conflicting. For example, providing `location` when `delivery_mode` is "ONLINE" (where it should be omitted), or an instructor ID that doesn’t match the authenticated professor creating the course.
* `ERR_UNKNOWN_FIELD` – **400 Bad Request** – The request body contains properties that are not defined by the API schema. The presence of any extraneous/unrecognized field causes the request to be rejected to enforce strict schema adherence.

#### **State & Business Rule Errors**

These errors indicate that the request was well-formed but violated business logic or resource state constraints. Typically **409 Conflict** is used for state-related conflicts, and **422 Unprocessable Entity** for other business rule violations (though 409 is common here).

* `ERR_COURSE_WRONG_STATE` – **409 Conflict** – The target course is not in an appropriate state to perform the requested action. (E.g. trying to publish a course that is already OPEN, or enroll in a course that isn’t OPEN, trying to cancel a course that is already completed, etc.) The error message will include the current state and what was required.
* `ERR_ENROLLMENT_WRONG_STATE` – **409 Conflict** – The enrollment’s state is not appropriate for the operation. (E.g. attempting to drop an enrollment that is already dropped or completed.)
* `ERR_TERM_NOT_ACTIVE` – **409 Conflict** – The AcademicTerm is not in a state that allows the operation. For instance, trying to create or publish courses while the term is CONCLUDED, or enrolling a student when the term’s state is not ENROLLMENT_OPEN.
* `ERR_REGISTRATION_CLOSED` – **409 Conflict** – Attempted to enroll or create an enrollment after the registration period is closed (term state is ENROLLMENT_CLOSED or CONCLUDED).
* `ERR_COURSE_FULL` – **409 Conflict** – The course has no available seats (capacity reached) and the action required a seat. This is returned when a Student attempts to enroll in a full course (instead of silently putting on waitlist, the system will create a waitlist entry but still may return a different indicator—however, in our design, a successful enrollment request returns success even if waitlisted, so this error is used primarily if someone attempts to force enroll beyond capacity in a disallowed way).
* `ERR_ALREADY_ENROLLED` – **409 Conflict** – The student is already enrolled (or waitlisted) in the given course and attempted to enroll again. Duplicate enrollments are not allowed.
* `ERR_NOT_ENROLLED` – **409 Conflict** – The student (or user) attempted to drop or manipulate an enrollment for a course in which they are not enrolled (and not even waitlisted). Also used if a Professor tries to drop a student who isn’t enrolled in their course.
* `ERR_MAX_COURSES_REACHED` – **409 Conflict** – The Professor has reached the maximum number of courses they can teach this term (creating another course would exceed `MAX_COURSES_PER_PROF`).
* `ERR_CREDIT_LIMIT_EXCEEDED` – **409 Conflict** – The student’s total enrolled credits would exceed the allowed limit (`MAX_CREDITS_PER_TERM`) by enrolling in this course. This error appears for Students attempting to overload themselves. (Registrars can override this – see hierarchy rules.)
* `ERR_TOO_MANY_DROPS` – **409 Conflict** – The student has already dropped the maximum allowed courses (`MAX_DROP_COUNT_PER_TERM`). The attempted drop is rejected. (At drop count == MAX_DROPS_PER_TERM, dropping is blocked.)
* `ERR_ILLEGAL_COURSE_STATE_TRANSITION` – **409 Conflict** – Attempt to force a Course.state transition that is not allowed (e.g. an undefined event or direct state jump).
* `ERR_ILLEGAL_ENROLLMENT_STATE` – **409 Conflict** – Attempt to set or force an Enrollment.state to something not allowed (e.g. manually setting an enrollment to COMPLETED via wrong endpoint, etc.).

#### **Ledger & Financial Errors**

Errors related to ledger operations or financial constraints:

* `ERR_INSUFFICIENT_FUNDS` – **402 Payment Required** – The student attempted an operation requiring payment (not in our current endpoints, but included for completeness) or the system expected a ledger balance and found it lacking (e.g. not directly used here as we don’t have partial payments affecting enrollment).
* `ERR_OVERPAY_NOT_ALLOWED` – **422 Unprocessable Entity** – The payment amount exceeds the student’s outstanding balance (would result in negative balance). The system does not allow overshooting; the caller must adjust the amount.
* `ERR_INVALID_PAYMENT_AMOUNT` – **422 Unprocessable Entity** – The payment amount provided is invalid (zero or negative, or not a whole number of cents).
* `ERR_LEDGER_MISSING` – **500 Internal Server Error** – (Should not occur in normal operations) Indicates a ledger entry expected to exist was not found. For example, if an enrollment tries to credit a StudentTuitionLedger that was not initialized and automatic init failed. This is a server-side error indicating misconfiguration.

Each error `error_id` is accompanied by a message in responses for clarity. The client should use the `error_id` to handle specific error cases (since messages may not be stable for parsing). The system ensures that in scenarios where multiple validation failures occur, the **most relevant error** is returned. Generally, **hierarchy of precedence** for errors is: Container-level checks (Term state, existence) are evaluated first, followed by resource existence, then permission/RBAC, then specific business validations (like capacity, credit limits), and finally payload format issues. This means, for example, if a term is concluded, an enrollment attempt will be blocked by `ERR_TERM_NOT_ACTIVE` even if the course might also be full; if the user is not authorized, you get an authorization error before any deeper validation, etc. This hierarchy prevents information leakage and ensures consistent error handling.

# Endpoints

This section outlines the API endpoints, including their routes, allowed methods, and high-level behavior. All endpoints are scoped under an AcademicTerm (indicated by `/terms/{termId}` in the path) to enforce container isolation.

**Note:** All requests require appropriate `X-User-ID` and `X-User-Role` headers to identify the acting user and their role. If these are missing or invalid, the request is rejected with `ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER` (400 Bad Request).

For brevity, the common response envelope is omitted in examples; assume all responses wrap the data or error in the standard envelope with `meta` and `data`. Pagination parameters (`limit`, `offset`) are supported on list endpoints.

### **1. Term Management**

* **POST /terms** – *Create a new AcademicTerm.*
  **Roles:** Registrar only.
  Creates a new academic term container. The Registrar provides term details (e.g. name). The term starts in `PLANNING` state by default. Response: 201 Created with the AcademicTerm object.

* **GET /terms/{termId}** – *Get AcademicTerm details.*
  **Roles:** Registrar and Professor (if needed to see term info), Students have no particular sensitive data here so they can also retrieve basic term info.
  Returns the AcademicTerm object, including its current state. This can be used to check if registration is open, etc. There are no role-specific field differences on the term itself (all roles see the same fields).

* **PATCH /terms/{termId}:open-registration** – *Open registration for a term.*
  **Roles:** Registrar only.
  Transition the term from `PLANNING` to `ENROLLMENT_OPEN`. This allows course publishing and student enrollment. This endpoint will fail if the term is not currently in PLANNING (error `ERR_TERM_WRONG_STATE` or `ERR_TERM_NOT_FOUND` if invalid). On success, term state becomes ENROLLMENT_OPEN (200 OK with updated term).

* **PATCH /terms/{termId}:close-registration** – *Close the enrollment period for a term.*
  **Roles:** Registrar only.
  Moves the term from `ENROLLMENT_OPEN` to `ENROLLMENT_CLOSED`. This action typically triggers all OPEN courses to transition to IN_PROGRESS (preventing further student-initiated enrollments). Response includes the updated term state. After this point, student enrollment endpoints will be blocked (registration closed). Returns 200 OK. (This does **not** yet conclude the term; classes are in session.)

* **PATCH /terms/{termId}:conclude** – *Conclude the term.*
  **Roles:** Registrar only.
  Finalizes the term by transitioning it to `CONCLUDED`. This triggers a cascade of completions: any course still in IN_PROGRESS will move to COMPLETED, and any active enrollments will be marked COMPLETED as appropriate (or DROPPED if they never got off waitlist). After concluding, no further modifications are allowed in the term. Returns 200 OK with updated term. Attempting to conclude a term that is not `ENROLLMENT_CLOSED` (i.e. if registration still open) will be rejected (must close registration first).

### **2. Course Management**

All course-related endpoints are under `/terms/{termId}/courses`.

* **POST /terms/{termId}/courses** – *Create a new Course in a term.*
  **Roles:** Professor (to create their own course), Registrar (to create any course).
  **Request Body:** Course fields (`code`, `title`, `description`, `credits`, `capacity`, and optionally `professor_id`, etc).

  * If a Professor calls this, they are implicitly the instructor: `professor_id` in the body must either be absent or equal to their own ID; otherwise `ERR_FIELD_CONFLICT`. The created course’s `professor_id` will be set to auth user and state = DRAFT.
  * If a Registrar calls, `professor_id` is required to assign an instructor. The course starts as DRAFT as well.
    The course is created in DRAFT state (not visible to students yet) and returned with 201 Created. Validation will ensure the course code is unique in the term, fields meet formatting rules, etc. If the professor already has `MAX_COURSES_PER_PROF` in this term, this call fails with `ERR_MAX_COURSES_REACHED`.

* **GET /terms/{termId}/courses** – *List courses in a term.*
  **Roles:** All roles (Student, Professor, Registrar) can call this, but results vary by role.
  Returns a paginated list of courses in the term. By default, only **published** courses (state != DRAFT) are listed to Students. Professors see their own courses regardless of state (so they can see their DRAFT courses in the list, whereas other professors’ DRAFTs are hidden). The Registrar sees all courses in the term including DRAFTs.
  **Field-level filtering:** The course objects in the list are redacted based on role:

  * Students: see basic info (code, title, credits, professor name/id) and *derived availability* (e.g. seats available), but not the `enrolled_count` or roster. They do not see courses that are DRAFT or CANCELLED (those are omitted or appear as if not present).
  * Professors: see all fields for their own courses (including `enrolled_count`, `waitlist_count`, etc.), and basic info for others’ courses (like a student would).
  * Registrar: sees all fields for all courses.
    This endpoint supports filtering by `state` or `professor_id` via query parameters for admin use (e.g. a Registrar could list all DRAFT courses to see what’s pending publication).

* **GET /terms/{termId}/courses/{courseId}** – *Get course details.*
  **Roles:** All roles, with data scoped by role.
  Returns the full detail of a course if permitted.

  * **Students:** If the course is OPEN/IN_PROGRESS/COMPLETED, they get the course info. If the student is **enrolled or waitlisted** in the course, an additional field `is_enrolled:true` or `is_waitlisted:true` is included in the data for their context, and they will also see their own enrollment status or grade (if completed) in the context of this course. If the student is not involved in the course, those context-specific flags are false or omitted. Students do **not** see the `enrolled_count` or `waitlist_count` directly (though they see available_seats). They do not see the list of other students or any grade distributions, etc.
  * **Professors:** If this is the professor’s own course, they see all course fields including `enrolled_count`, `waitlist_count`, and they get a list of enrollments (roster) as an array under, say, `enrollments` key with each enrollment’s student_id and state (and grade if completed). If a professor requests a course they do NOT teach and it’s not published, it will appear not found (DRAFT hidden). If it’s published and not theirs, they see the same view a student would see (no roster).
  * **Registrar:** Sees all fields: full roster of enrollments with student IDs and states, counts, and even financial info (e.g., could include an aggregate of total tuition from that course if we derived it, though that’s not explicitly a field here). Essentially, no data is redacted for Registrar.
    If the course is in DRAFT and the caller is not the instructor or Registrar, the response is `404 ERR_COURSE_NOT_FOUND`. If the course is CANCELLED, students who were not in it will get 404 as well (treat cancelled courses as gone for those not involved, to avoid confusion).

* **PATCH /terms/{termId}/courses/{courseId}:publish** – *Publish a course (move from DRAFT to OPEN).*
  **Roles:** Professor (if it’s their course), or Registrar.
  Publishes a draft course, making it available for student enrollment (course state -> OPEN). This sets the course’s `state` to OPEN and initializes its seat ledger (`CourseSeatLedger`) with `seats_available = capacity`. After this call, students can see the course in listings.
  Validation: The course must be in DRAFT (`ERR_COURSE_WRONG_STATE` if not). The AcademicTerm must be in ENROLLMENT_OPEN (`ERR_TERM_NOT_ACTIVE` if the term isn’t currently allowing enrollment). Only the course’s instructor or Registrar can publish (else `ERR_NOT_INSTRUCTOR`). On success, returns 200 with the updated course (state now OPEN). The `updated_at` and `revision` increment. Students on the waitlist (should be none in draft) is not applicable here.

* **PATCH /terms/{termId}/courses/{courseId}:cancel** – *Cancel a course.*
  **Roles:** Professor (if course instructor) or Registrar.
  Cancels an active or draft course (state -> CANCELLED). This operation has significant side effects: all enrollments for this course will be dropped and ledgers adjusted (see Business Rules). It can be invoked in DRAFT, OPEN, or IN_PROGRESS states. If successful, the course state becomes CANCELLED and is essentially closed.
  Validation: Only instructor or Registrar can cancel (`ERR_NOT_INSTRUCTOR` if not authorized). If course is already COMPLETED or already CANCELLED, it cannot be cancelled again (`ERR_COURSE_WRONG_STATE`). On success, returns 200 OK with course now in CANCELLED state. (Enrollments changes are not individually returned here, but their states will have changed to DROPPED internally.)

*(There is no direct endpoint to edit course details like title or capacity in this spec for brevity, but minor updates could be done via a generic PATCH if needed with similar validations; we focus on the core actions.)*

### **3. Enrollment & Waitlist Management**

All enrollment endpoints are under `/terms/{termId}/courses/{courseId}/enrollments`.

* **POST /terms/{termId}/courses/{courseId}/enrollments** – *Student enroll in a course.*
  **Roles:** Student (to enroll themselves), Registrar (to enroll a student administratively). Professors typically do not call this (they cannot enroll other students via API, except the Registrar can as override).
  **Request Body:** For Students, typically no body needed except maybe an optional field if we wanted like `audit=true` for auditing (not in scope). For Registrar, must specify `{ "student_id": "<id>" }` to indicate whom to enroll.
  Behavior: Attempts to create a new enrollment for the given course.

  * Validations:

    * The target course must exist under the term and be **OPEN** for enrollment (`ERR_COURSE_NOT_FOUND` or `ERR_COURSE_WRONG_STATE` if not). Also the term itself must be in ENROLLMENT_OPEN (`ERR_REGISTRATION_CLOSED` if term closed).
    * **RBAC**: If Student, they can only enroll themselves. If a `student_id` is provided and not equal to auth user, throw `ERR_FORBIDDEN` or specifically handle via `ERR_FIELD_CONFLICT` (they shouldn’t provide someone else’s ID). The system will use `auth.id` as the enrolling student_id in that case. If Registrar, a `student_id` in body is required and that student must exist and have role Student (`ERR_STUDENT_NOT_FOUND` if not).
    * Check the student is not already enrolled or waitlisted in that course (`ERR_ALREADY_ENROLLED` if so).
    * Check the course state: must be OPEN. If the course is not OPEN (or term not open), reject (error as above).
    * Check capacity: if `CourseSeatLedger.seats_available > 0`, a seat is free. If 0, the student will be waitlisted instead of error (we consider waitlisting a valid outcome, not a failure). The logic will determine enrollment state accordingly (ENROLLED vs WAITLISTED).
    * Check student’s credit load: Sum of credits of all the student’s ENROLLED courses in this term plus this course’s credits must be ≤ `MAX_CREDITS_PER_TERM` (18). If this would exceed and the caller is a Student, reject with `ERR_CREDIT_LIMIT_EXCEEDED`. (If caller is Registrar, this rule is **bypassed** – the Registrar can overload a student, assuming an override scenario.)
    * If professor’s course count limit or any other systemic invariants, those would have been handled at course creation side, not here.
  * Actions:

    * If seats are available: create the Enrollment with state = ENROLLED.

      * Debit the CourseSeatLedger by 1 (reduce available seats).
      * Credit the StudentTuitionLedger for that student by `credits * COST_PER_CREDIT` (increasing their balance owed).
    * If no seats available: create Enrollment with state = WAITLISTED.

      * (Do *not* modify seat ledger, since no seat taken.)
      * Do not charge tuition yet (ledger unchanged for that student).
    * Set `created_at` timestamp, etc. If ledger entry for the student doesn’t exist yet (first enrollment of term), initialize it with balance 0 before crediting.
    * Return 201 Created. The response data includes the enrollment resource. If waitlisted, the client can tell by the state in the returned object. (Alternatively, the API could return 200 with a different message or include a header, but here we simply reflect state.)
  * Additional: If course was waitlisted and a seat opens later, the promotion is handled asynchronously by the system (see Business Rules for the event-driven promotion logic). The client would need to GET the enrollment or receive an out-of-band notification (not covered here) to know it moved to ENROLLED.

* **GET /terms/{termId}/courses/{courseId}/enrollments** – *List enrollments (roster) of a course.*
  **Roles:** Professor (for their own course), Registrar. Students are **not** allowed to list all enrollments of a course (privacy reasons). A student trying to call this will get `403 ERR_UNAUTHORIZED_ROLE` (or 404 if we want to hide existence).
  Returns a list of enrollment records for the specified course. Each record includes at least `id, student_id, state`. The professor only sees enrollments for their course; the Registrar can list for any course. Optionally, the response could include student names or profiles if that were part of the system, but here just IDs and states. Waitlisted students are included in the list with state WAITLISTED. This allows the instructor to see the waitlist queue (ordered by created_at). Role-based filtering: Registrar sees all fields (including perhaps each student’s grade if completed, and can see their tuition status if needed via other endpoints). Professors see grades for their students after completion as well, but not the students’ financial info.

* **GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}** – *Get a specific enrollment record.*
  **Roles:** The student who owns the enrollment, the professor of the course, or Registrar.
  This returns the enrollment details.

  * If a Student is requesting, they must be the `student_id` on that enrollment or they receive 404/403. They will see their enrollment state and if completed, the grade. They will not see any information that isn’t about them (since the resource is specifically their record anyway).
  * A Professor can retrieve an enrollment for their course (e.g. to view a specific student’s status or grade). They get the state and grade of that enrollment.
  * Registrar can retrieve any enrollment.
    If the enrollmentId is not for the specified term/course combination or the user isn’t allowed, `ERR_ENROLLMENT_NOT_FOUND` or `ERR_UNAUTHORIZED_ROLE` is returned.

* **PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop** – *Drop/withdraw from a course.*
  **Roles:** Student (to drop their own enrollment), Professor (to remove a student from *their* course), Registrar (to administratively drop any student).
  This endpoint handles both student-initiated drops and administrative removals. It transitions an enrollment to DROPPED state and triggers refunds/seat availability updates.

  * Validations:

    * The enrollment must exist and belong to the course/term given (else `ERR_ENROLLMENT_NOT_FOUND`).
    * The caller must be authorized:

      * A student can only drop their own enrollment (`auth.id` must match enrollment.student_id, otherwise `ERR_FORBIDDEN`).
      * A professor can drop an enrollment only if they are the course’s instructor (`ERR_NOT_INSTRUCTOR` if not) – this covers removing a student from their class.
      * Registrar can drop any enrollment.
    * The enrollment’s current state must be ENROLLED or WAITLISTED to drop. If it’s already DROPPED or COMPLETED, this operation is not allowed (`ERR_ENROLLMENT_WRONG_STATE`).
    * Term/state constraints: Students cannot drop after the term is CONCLUDED (`ERR_TERM_NOT_ACTIVE` if term already concluded – though by then it’d be completed, but just in case). Also if course is already completed, dropping is not applicable.
    * Drop count policy (for student dropping themselves): The system counts how many courses the student has already dropped in this term (not including this one). If this is their 4th drop attempt (i.e. they have 3 prior DROPPED enrollments), the drop is disallowed with `ERR_TOO_MANY_DROPS`. If it’s their 3rd drop (2 prior), the drop will be allowed but incur a penalty fee in ledger (see Actions).
  * Actions:

    * Transition the enrollment state to DROPPED.
    * If the enrollment was ENROLLED (the student had a seat):

      * Credit the CourseSeatLedger by 1 (free up a seat).
      * Debit the StudentTuitionLedger by the course cost (effectively issuing a refund, reducing their owed balance). **However**, if the drop is occurring after a certain point (not modeled by time here, so we assume any drop in ENROLLMENT_OPEN or IN_PROGRESS triggers full refund for simplicity, except when penalty applies as below).
      * If the drop triggers a seat opening and the course has waitlisted students, an event is raised (see Business Rules “seat_vacated” event) to auto-enroll the next waitlisted student.
    * If the enrollment was WAITLISTED (no seat taken yet):

      * Remove from waitlist (no ledger seat changes needed).
      * No tuition was charged, so no refund necessary.
    * If the drop is being done by the student themselves (auth.role=Student):

      * Increment their drop count. If this drop count (after performing drop) reaches 3 (meaning this is the 3rd course they have dropped in the term), apply a penalty: **credit** (add) `DROP_PENALTY_FEE` (5000 cents) to their StudentTuitionLedger balance. This represents an administrative fee for excessive dropping, making future tuition owed higher. (A log will record this fee application.)
      * If the drop count would have exceeded 3 (should have been caught in validation for the 4th attempt and not allowed).
    * The endpoint returns 200 OK on success with the updated enrollment resource (now state DROPPED). The client can infer from the state that the student is no longer in the course.
    * Side effects like seat promotion will happen asynchronously (immediately triggered, but via a separate rule execution, not directly in this response).

* **POST /terms/{termId}/students/{studentId}:pay** – *Make a tuition payment.*
  **Roles:** Student (pay their own balance), Registrar (record a payment for a student).
  **Request Body:** `{ "amount": <positive_integer_cents> }` indicating the payment amount in cents.
  Deducts from the student’s outstanding balance for the term.

  * Validations:

    * If auth.role = Student, `studentId` in path must equal auth.id (`ERR_FORBIDDEN` if not). Students can only pay for themselves.
    * If auth.role = Registrar, any `studentId` is allowed (must exist as a student in the term context, else `ERR_STUDENT_NOT_FOUND`).
    * Amount must be a positive integer > 0 (`ERR_INVALID_PAYMENT_AMOUNT` if not).
    * The StudentTuitionLedger for that student must exist. If it does not (no enrollments yet and no ledger), initialize it to 0 before proceeding.
    * The amount must not exceed the current balance (`ERR_OVERPAY_NOT_ALLOWED` if paying more than owed).
  * Actions:

    * Perform a **debit** on StudentTuitionLedger[termId, studentId] for the given amount (reducing the balance by that amount).
    * If the balance reaches exactly 0 with this payment, optionally an event could be logged (like fully paid off, etc., but no additional rule effect in this spec aside from maybe audit).
    * No state changes; this is purely a financial operation.
    * Return 200 OK with an object containing the updated ledger balance (or the whole ledger entry). For example, `{ "student_id": X, "term_id": Y, "new_balance": 12345 }`. The `new_balance` after payment is useful for the client.

All endpoints that modify resources (POST, PATCH) use optimistic concurrency via the `revision` field where applicable. The client must supply the latest `revision` (e.g. in the body for updates) and the server will check it matches; if not, the request fails with `ERR_REV_CONFLICT` (409 Conflict), meaning the resource was updated by someone else in the meantime. This applies to endpoints like publishing or cancelling courses, and possibly dropping enrollments if we choose to require the enrollment’s revision (to prevent double drop race conditions).

Additionally, any endpoint can produce errors from the Error Catalogue above depending on what validation fails. For example, trying to enroll in a course in the wrong term yields ERR_COURSE_NOT_FOUND (since the course lookup by term will fail). The **Business Rules** section below details the precise checks and actions for each operation.

# Business Rules

This section formally defines the business logic for validations and side effects of each endpoint, as well as event-triggered rules that handle cross-resource interactions. Rules are written in a Specdown-like pseudocode for clarity, grouping validations and actions. Each rule either corresponds to an endpoint (`WHEN: HTTP VERB /...`) or an internal event (`WHEN: on_event ...`).

## Rule: create-term

**WHEN:** `POST /terms`
**WHO:** `auth.role == 'REGISTRAR'` only.
**VALIDATE:**

* CHECK: auth.role == 'REGISTRAR'

  * FAIL_WITH: 403, `ERR_UNAUTHORIZED_ROLE`
* CHECK: body.name != null && body.name.length > 0

  * FAIL_WITH: 400, `ERR_MISSING_REQUIRED_FIELD` (Term name is required)
* CHECK: @AcademicTerm where name == body.name count == 0

  * FAIL_WITH: 409, `ERR_TERM_NAME_NOT_UNIQUE` (Term names must be unique.)
* (No state checks needed; creating a term is always allowed for Registrar.)
  **ACTIONS:**
* DO: let term = resource.create(@AcademicTerm, {
  id: uuid(),
  name: body.name,
  state: 'PLANNING',
  created_by: auth.id,
  created_at: now(),
  revision: 0
  })
* DO: audit.log('term_created', term.id, { created_by: auth.id })
  *(Result: AcademicTerm created. No ledgers directly associated on term creation.)*

## Rule: open-term-registration

**WHEN:** `PATCH /terms/{termId}:open-registration`
**WHO:** `auth.role == 'REGISTRAR'`
**VALIDATE:**

* CHECK: auth.role == 'REGISTRAR'

  * FAIL_WITH: 403, `ERR_UNAUTHORIZED_ROLE`
* CHECK: @AcademicTerm[path.termId].exists()

  * FAIL_WITH: 404, `ERR_TERM_NOT_FOUND`
* CHECK: @AcademicTerm[path.termId].state == 'PLANNING'

  * FAIL_WITH: 409, `ERR_TERM_NOT_ACTIVE` (Only can open if currently PLANNING)
* CHECK: body.revision == @AcademicTerm[path.termId].revision

  * FAIL_WITH: 409, `ERR_REV_CONFLICT` (Term was updated by someone else)
    **ACTIONS:**
* DO: state.transition(@AcademicTerm[path.termId], 'ENROLLMENT_OPEN')
* DO: resource.update(@AcademicTerm[path.termId], { revision: +1 })
* DO: audit.log('term_opened', { termId: path.termId, opened_by: auth.id })

*(When a term opens, no immediate changes to courses; they remain DRAFT until published. But courses can now be published since term is open.)*

## Rule: close-term-registration

**WHEN:** `PATCH /terms/{termId}:close-registration`
**WHO:** `auth.role == 'REGISTRAR'`
**VALIDATE:**

* CHECK: auth.role == 'REGISTRAR'

  * FAIL_WITH: 403, `ERR_UNAUTHORIZED_ROLE`
* CHECK: @AcademicTerm[path.termId].exists()

  * FAIL_WITH: 404, `ERR_TERM_NOT_FOUND`
* CHECK: @AcademicTerm[path.termId].state == 'ENROLLMENT_OPEN'

  * FAIL_WITH: 409, `ERR_TERM_NOT_ACTIVE` (Can only close if registration currently open)
* CHECK: body.revision == @AcademicTerm[path.termId].revision

  * FAIL_WITH: 409, `ERR_REV_CONFLICT`
    **ACTIONS:**
* DO: state.transition(@AcademicTerm[path.termId], 'ENROLLMENT_CLOSED')
* DO: resource.update(@AcademicTerm[path.termId], { revision: +1 })
* DO: audit.log('term_closed', { termId: path.termId, closed_by: auth.id })
* DO: foreach(course in @Course where term_id == path.termId and state == 'OPEN') {
  state.transition(course, 'IN_PROGRESS');
  audit.log('course_in_progress', { courseId: course.id, termId: path.termId })
  }
  *(This automatically advances all open courses to in-progress since registration is now closed. Students can no longer self-enroll via rules that will follow.)*

## Rule: conclude-term (NEXUS)

**WHEN:** `PATCH /terms/{termId}:conclude`
**WHO:** `auth.role == 'REGISTRAR'`
**VALIDATE:**

* CHECK: auth.role == 'REGISTRAR'

  * FAIL_WITH: 403, `ERR_UNAUTHORIZED_ROLE`
* CHECK: @AcademicTerm[path.termId].exists()

  * FAIL_WITH: 404, `ERR_TERM_NOT_FOUND`
* CHECK: @AcademicTerm[path.termId].state == 'ENROLLMENT_CLOSED'

  * FAIL_WITH: 409, `ERR_TERM_NOT_ACTIVE` (You must close registration before concluding)
* CHECK: body.revision == @AcademicTerm[path.termId].revision

  * FAIL_WITH: 409, `ERR_REV_CONFLICT`
    **ACTIONS:**
* DO: state.transition(@AcademicTerm[path.termId], 'CONCLUDED')
* DO: resource.update(@AcademicTerm[path.termId], { revision: +1 })
* DO: audit.log('term_concluded', { termId: path.termId, by: auth.id })
* DO: **(cascade)** for each course in @Course where term_id == path.termId and state not in ['COMPLETED','CANCELLED']:

  * state.transition(course, 'COMPLETED');
  * audit.log('course_completed', { courseId: course.id, termId: path.termId });
  * for each enrollment in @Enrollment where course_id == course.id:
    if(enrollment.state == 'ENROLLED') {
    state.transition(enrollment, 'COMPLETED');
    audit.log('enrollment_completed', { enrollId: enrollment.id, student: enrollment.student_id });
    } else if(enrollment.state == 'WAITLISTED') {
    state.transition(enrollment, 'DROPPED');  // never got off waitlist
    audit.log('enrollment_closed_unfulfilled', { enrollId: enrollment.id });
    }
* DO: (optional) for each student in @StudentTuitionLedger where term_id == path.termId: if(ledger.balance_cents > 0) {
  // optionally flag that they owe money post-term, but no action in this spec
  }

*(This rule is a **NEXUS** of multiple levers: it involves a container-level state change (term), triggers cross-resource state transitions (courses and enrollments), and interacts with ledgers indirectly in that no more ledger changes happen after this. It demonstrates emergent behavior: concluding a term causes a cascade of completions and drops throughout the system.)*

## Rule: create-course

**WHEN:** `POST /terms/{termId}/courses`
**WHO:** `auth.role == 'PROFESSOR' or 'REGISTRAR'`
**VALIDATE:**

* CHECK: auth.role in ['PROFESSOR','REGISTRAR']

  * FAIL_WITH: 403, `ERR_UNAUTHORIZED_ROLE` (Only prof or registrar can create courses)
* CHECK: @AcademicTerm[path.termId].exists() && (@AcademicTerm[path.termId].state != 'CONCLUDED')

  * FAIL_WITH: 404, `ERR_TERM_NOT_FOUND` (If term doesn't exist or is archived, treat as not found; also if concluded, effectively closed for new courses, could use ERR_TERM_NOT_ACTIVE)
* CHECK: body.code matches ^[A-Z]{2,4}[0-9]{3}$

  * FAIL_WITH: 400, `ERR_INVALID_COURSE_CODE`
* CHECK: @Course where term_id==path.termId and code==body.code count == 0

  * FAIL_WITH: 409, `ERR_COURSE_CODE_NOT_UNIQUE` (Unique code constraint)
* CHECK: body.credits >= 1 && body.credits <= 5

  * FAIL_WITH: 400, `ERR_INVALID_CREDITS`
* CHECK: body.capacity >= 1 && body.capacity <= 500

  * FAIL_WITH: 400, `ERR_INVALID_CAPACITY`
* CHECK: if(auth.role == 'PROFESSOR') then (body.professor_id is null or body.professor_id == auth.id)

  * FAIL_WITH: 400, `ERR_FIELD_CONFLICT` (Professors cannot specify a different instructor)
* CHECK: if(auth.role == 'REGISTRAR') then body.professor_id != null

  * FAIL_WITH: 400, `ERR_MISSING_REQUIRED_FIELD` (Registrar must assign a professor)
* CHECK: if(auth.role == 'REGISTRAR') then @User[body.professor_id].role == 'PROFESSOR'

  * FAIL_WITH: 422, `ERR_INVALID_INSTRUCTOR` (Provided instructor is not a professor role or doesn’t exist)
* CHECK: if(auth.role == 'PROFESSOR') then @Course where term_id==path.termId and professor_id == auth.id count < MAX_COURSES_PER_PROF

  * FAIL_WITH: 409, `ERR_MAX_COURSES_REACHED` (Professor already at course limit)
* CHECK: body.delivery_mode == 'IN_PERSON' implies body.location is provided (non-empty)

  * FAIL_WITH: 400, `ERR_CONDITIONAL_FIELD_REQUIRED` (Location required for in-person course)
* CHECK: body.delivery_mode == 'ONLINE' implies body.online_link is provided

  * FAIL_WITH: 400, `ERR_CONDITIONAL_FIELD_REQUIRED` (Online link required for online course)
* CHECK: body.location is provided implies body.delivery_mode == 'IN_PERSON'

  * FAIL_WITH: 400, `ERR_FIELD_CONFLICT` (Location given for a course not marked in-person)
* CHECK: body.online_link is provided implies body.delivery_mode == 'ONLINE'

  * FAIL_WITH: 400, `ERR_FIELD_CONFLICT` (Online link given for a course not marked online)
    *(Above two ensure no irrelevant fields)*
    **ACTIONS:**
* DO: let profId = (auth.role == 'PROFESSOR') ? auth.id : body.professor_id
* DO: let course = resource.create(@Course, {
  id: uuid(),
  term_id: path.termId,
  code: body.code,
  title: body.title,
  description: body.description,
  credits: body.credits,
  capacity: body.capacity,
  professor_id: profId,
  state: 'DRAFT',
  enrolled_count: 0,  // derived ultimately
  waitlist_count: 0,
  available_seats: 0,  // not open yet
  created_at: now(),
  updated_at: now(),
  revision: 0
  })
* DO: audit.log('course_created', { courseId: course.id, termId: path.termId, by: auth.id, professor: profId })
  *(No ledger operations on create; seat ledger will be initialized on publish.)*

## Rule: publish-course

**WHEN:** `PATCH /terms/{termId}/courses/{courseId}:publish`
**WHO:** `auth.role == 'PROFESSOR' or 'REGISTRAR'`
**VALIDATE:**

* CHECK: @Course[path.courseId].term_id == path.termId

  * FAIL_WITH: 404, `ERR_COURSE_NOT_FOUND`
* CHECK: auth.role == 'REGISTRAR' or (@Course[path.courseId].professor_id == auth.id)

  * FAIL_WITH: 403, `ERR_NOT_INSTRUCTOR`
* CHECK: @Course[path.courseId].state == 'DRAFT'

  * FAIL_WITH: 409, `ERR_COURSE_WRONG_STATE`
* CHECK: @AcademicTerm[path.termId].state == 'ENROLLMENT_OPEN'

  * FAIL_WITH: 409, `ERR_TERM_NOT_ACTIVE` (Term must be open to publish courses)
* CHECK: body.revision == @Course[path.courseId].revision

  * FAIL_WITH: 409, `ERR_REV_CONFLICT`
    **ACTIONS:**
* DO: state.transition(@Course[path.courseId], 'OPEN')
* DO: resource.update(@Course[path.courseId], {
  updated_at: now(),
  revision: +1
  })
* DO: ledger.init(@Ledger:CourseSeatLedger, { term_id: path.termId, course_id: path.courseId, seats_available: @Course[path.courseId].capacity })

  * (Initialize the CourseSeatLedger entry for this course with full capacity. This is effectively the first time seats_available becomes equal to capacity.)
* DO: audit.log('course_published', { courseId: path.courseId, termId: path.termId, by: auth.id })
  *(After this, students can find and enroll in the course. If ledger.init indicates the entry already exists (shouldn’t, since course was draft and no one could enroll), we skip init.)*

## Rule: cancel-course (NEXUS)

**WHEN:** `PATCH /terms/{termId}/courses/{courseId}:cancel`
**WHO:** `auth.role == 'PROFESSOR' or 'REGISTRAR'`
**VALIDATE:**

* CHECK: @Course[path.courseId].term_id == path.termId

  * FAIL_WITH: 404, `ERR_COURSE_NOT_FOUND`
* CHECK: auth.role == 'REGISTRAR' or (@Course[path.courseId].professor_id == auth.id)

  * FAIL_WITH: 403, `ERR_NOT_INSTRUCTOR`
* CHECK: @Course[path.courseId].state not in ['COMPLETED','CANCELLED']

  * FAIL_WITH: 409, `ERR_COURSE_WRONG_STATE` (Already completed or cancelled courses cannot be cancelled)
* CHECK: body.revision == @Course[path.courseId].revision

  * FAIL_WITH: 409, `ERR_REV_CONFLICT`
    **ACTIONS:**
* DO: state.transition(@Course[path.courseId], 'CANCELLED')
* DO: resource.update(@Course[path.courseId], { updated_at: now(), revision: +1 })
* DO: audit.log('course_cancelled', { courseId: path.courseId, termId: path.termId, by: auth.id })
* DO: **(cascade drops)** for each enrollment in @Enrollment where course_id == path.courseId and state in ['ENROLLED','WAITLISTED']:

  * state.transition(enrollment, 'DROPPED')
  * if(enrollment.state was 'ENROLLED') {
    // ledger and refund only if was enrolled
    ledger.credit(@Ledger:CourseSeatLedger, 'seats_available', 1)
    ledger.debit(@Ledger:StudentTuitionLedger, 'balance_cents', enrollment.course.credits * COST_PER_CREDIT)
    // (Return tuition for enrolled students)
    }
  * audit.log('enrollment_dropped_due_course_cancel', { enrollmentId: enrollment.id, student: enrollment.student_id, courseId: path.courseId })
* DO: // If needed, also handle any other cross effects: none specifically beyond dropping enrollments. (No seat vacated event is fired because course is cancelled entirely; waitlisted promotions don’t apply.)

*(This rule is a **NEXUS** because it involves RBAC (who can cancel), a state machine transition (course -> CANCELLED), ledger operations (refund tuition, free seats), and cross-resource side effects (dropping all enrollments).)*

## Rule: enroll-in-course (NEXUS)

**WHEN:** `POST /terms/{termId}/courses/{courseId}/enrollments`
**WHO:** `auth.role == 'STUDENT' or 'REGISTRAR'`
**VALIDATE:**

* CHECK: auth.role in ['STUDENT','REGISTRAR']

  * FAIL_WITH: 403, `ERR_UNAUTHORIZED_ROLE`
* CHECK: @AcademicTerm[path.termId].exists()

  * FAIL_WITH: 404, `ERR_TERM_NOT_FOUND`
* CHECK: @Course[path.courseId].term_id == path.termId

  * FAIL_WITH: 404, `ERR_COURSE_NOT_FOUND`
* CHECK: @Course[path.courseId].state == 'OPEN'

  * FAIL_WITH: 409, `ERR_COURSE_NOT_OPEN` (Course must be open for enrollment)
* CHECK: @AcademicTerm[path.termId].state == 'ENROLLMENT_OPEN'

  * FAIL_WITH: 409, `ERR_REGISTRATION_CLOSED` (Term must allow enrollment)
* CHECK: if(auth.role == 'STUDENT') then (body.student_id == null or body.student_id == auth.id)

  * FAIL_WITH: 403, `ERR_FORBIDDEN` (Students cannot enroll other students)
* CHECK: if(auth.role == 'REGISTRAR') then body.student_id != null

  * FAIL_WITH: 400, `ERR_MISSING_REQUIRED_FIELD` (Registrar must specify which student to enroll)
* CHECK: let targetStudentId = (auth.role == 'STUDENT' ? auth.id : body.student_id)
* CHECK: @User[targetStudentId].role == 'STUDENT'

  * FAIL_WITH: 404, `ERR_STUDENT_NOT_FOUND` (Target student must exist and be a student)
* CHECK: @Enrollment where course_id==path.courseId and student_id==targetStudentId and state in ['ENROLLED','WAITLISTED'] count == 0

  * FAIL_WITH: 409, `ERR_ALREADY_ENROLLED`
* CHECK: @Enrollment where student_id==targetStudentId and term_id==path.termId and state=='ENROLLED' sum(credits) + @Course[path.courseId].credits <= MAX_CREDITS_PER_TERM or auth.role == 'REGISTRAR'

  * FAIL_WITH: 409, `ERR_CREDIT_LIMIT_EXCEEDED` (Student cannot exceed credit limit on their own; Registrar override allowed)
* CHECK: @Course[path.courseId].capacity > 0 (redundant, capacity defined)
* (No explicit check for seats here; handled in actions based on ledger)
  **ACTIONS:**
* DO: let studentId = (auth.role == 'STUDENT' ? auth.id : body.student_id)
* DO: if(!@Ledger:StudentTuitionLedger.exists()) {
  ledger.init(@Ledger:StudentTuitionLedger, { term_id: path.termId, student_id: studentId, balance_cents: 0 })
  }
* DO: if(@Ledger:CourseSeatLedger.seats_available > 0) {
  // Seat is available
  let enrollment = resource.create(@Enrollment, {
  id: uuid(), term_id: path.termId, course_id: path.courseId, student_id: studentId,
  state: 'ENROLLED', created_at: now(), revision: 0
  });
  // consume a seat and charge tuition
  ledger.debit(@Ledger:CourseSeatLedger, 'seats_available', 1);
  ledger.credit(@Ledger:StudentTuitionLedger, 'balance_cents', @Course[path.courseId].credits * COST_PER_CREDIT);
  audit.log('student_enrolled', { courseId: path.courseId, student: studentId, enrollmentId: enrollment.id });
  } else {
  // No seat, go to waitlist
  let enrollment = resource.create(@Enrollment, {
  id: uuid(), term_id: path.termId, course_id: path.courseId, student_id: studentId,
  state: 'WAITLISTED', created_at: now(), revision: 0
  });
  audit.log('student_waitlisted', { courseId: path.courseId, student: studentId, enrollmentId: enrollment.id });
  }
* DO: // After creating, possibly update derived counts (the system will derive enrolled_count etc. via query or derivations, not directly set here).
* DO: // No fail on capacity full; we've handled by branching to waitlist rather than failing.

*(This enrollment rule is a **NEXUS** combining RBAC, state checks, ledger ops, and conditional logic for waitlisting. It doesn’t fail when full; it produces different outcome. Registrar override of credit limit is implicit by skipping that check.)*

## Rule: drop-enrollment (NEXUS)

**WHEN:** `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop`
**WHO:** `auth.role == 'STUDENT' or 'PROFESSOR' or 'REGISTRAR'`
**VALIDATE:**

* CHECK: @Enrollment[path.enrollmentId].exists() && @Enrollment[path.enrollmentId].course_id == path.courseId && @Course[path.courseId].term_id == path.termId

  * FAIL_WITH: 404, `ERR_ENROLLMENT_NOT_FOUND`
* CHECK: auth.role == 'REGISTRAR' or (auth.role == 'STUDENT' and auth.id == @Enrollment[path.enrollmentId].student_id) or (auth.role == 'PROFESSOR' and @Course[path.courseId].professor_id == auth.id)

  * FAIL_WITH: 403, `ERR_PERMISSION_DENIED` (Not allowed to drop this enrollment)
* CHECK: @Enrollment[path.enrollmentId].state in ['ENROLLED','WAITLISTED']

  * FAIL_WITH: 409, `ERR_ENROLLMENT_WRONG_STATE` (Already dropped or completed)
* CHECK: @AcademicTerm[path.termId].state not in ['CONCLUDED'] or auth.role == 'REGISTRAR'

  * FAIL_WITH: 409, `ERR_TERM_NOT_ACTIVE` (Students/Professors cannot drop after term concluded; Registrar can override)
* CHECK: if(auth.role == 'STUDENT') then ( @Enrollment where term_id==path.termId and student_id==auth.id and state=='DROPPED' count < MAX_DROP_COUNT_PER_TERM )

  * FAIL_WITH: 409, `ERR_TOO_MANY_DROPS` (Already dropped too many courses; this drop not allowed)
* CHECK: body.revision == @Enrollment[path.enrollmentId].revision

  * FAIL_WITH: 409, `ERR_REV_CONFLICT`
    **ACTIONS:**
* DO: let enrollment = @Enrollment[path.enrollmentId]
* DO: state.transition(enrollment, 'DROPPED')
* DO: resource.update(enrollment, { revision: +1 })
* DO: if(enrollment.state **was** 'ENROLLED' before drop) {
  ledger.credit(@Ledger:CourseSeatLedger, 'seats_available', 1);
  ledger.debit(@Ledger:StudentTuitionLedger, 'balance_cents', @Course[path.courseId].credits * COST_PER_CREDIT);
  audit.log('enrollment_dropped', { enrollmentId: enrollment.id, student: enrollment.student_id, course: path.courseId, type: 'ENROLLED_DROP' });
  // Trigger seat availability event if waitlist exists
  if(@Enrollment where course_id==path.courseId and state=='WAITLISTED' count > 0) {
  state.transition(@Course[path.courseId], 'seat_vacated', { courseId: path.courseId });
  }
  } else if(enrollment.state **was** 'WAITLISTED') {
  // If was waitlisted, simply removed from waitlist
  audit.log('enrollment_dropped', { enrollmentId: enrollment.id, student: enrollment.student_id, course: path.courseId, type: 'WAITLIST_DROP' });
  }
* DO: if(auth.role == 'STUDENT') {
  let priorDrops = @Enrollment where term_id==path.termId and student_id==auth.id and state=='DROPPED' count (after this drop included)
  if(priorDrops == MAX_DROP_COUNT_PER_TERM) {
  ledger.credit(@Ledger:StudentTuitionLedger, 'balance_cents', DROP_PENALTY_FEE);
  audit.log('drop_penalty_applied', { student: auth.id, term: path.termId, drops: priorDrops, fee: DROP_PENALTY_FEE });
  }
  }

*(This rule is a **NEXUS** of RBAC (role-based who can drop), state management (enrollment state change, potential course event), ledger updates (tuition refund, seat increment, penalty fee), and a cross-resource trigger (notifying course of seat freed).)*

## Rule: seat-availability (Event)

**WHEN:** `on_event seat_vacated`
**WHY:** Triggered when a seat becomes available in a course that has waitlisted students (emitted by drop or other actions).
**ACTIONS:**

* DO: let courseId = event.courseId
* DO: let waitlist = @Enrollment where course_id==courseId and state=='WAITLISTED' sort by created_at asc
* DO: if(waitlist.count > 0) {
  let promoteEnroll = waitlist[0];  // get oldest waitlisted
  // Promote this enrollment
  state.transition(promoteEnroll, 'ENROLLED');
  resource.update(promoteEnroll, { revision: +1 });
  // Deduct the freed seat (which was credited, now consume it)
  ledger.debit(@Ledger:CourseSeatLedger, 'seats_available', 1);
  // Charge tuition for this student now
  ledger.credit(@Ledger:StudentTuitionLedger, 'balance_cents', @Course[promoteEnroll.course_id].credits * COST_PER_CREDIT);
  audit.log('waitlist_promoted', { enrollmentId: promoteEnroll.id, student: promoteEnroll.student_id, course: promoteEnroll.course_id });
  if(@Enrollment where course_id==courseId and state=='WAITLISTED' count > 0 && @Ledger:CourseSeatLedger.seats_available > 0) {
  // If more seats freed (e.g., multiple drops at once) and still waitlisted folks, continue promoting
  state.transition(@Course[courseId], 'seat_vacated', { courseId: courseId });
  }
  }

*(This event-driven rule runs asynchronously when a seat is freed in a course that has a queue. It processes one waitlisted student at a time in FIFO order, promoting them to ENROLLED, updating ledgers and their enrollment state. If multiple seats become free (say a drop that freed 2 seats or consecutive drop events), the rule re-emits the event until no waitlist or no seats available. This ensures no seat remains unused while someone is waiting. This demonstrates **passive logic** and **chained consequences** – a drop causes a promotion, which could trigger further promotions.)*

## Rule: pay-tuition

**WHEN:** `POST /terms/{termId}/students/{studentId}:pay`
**WHO:** `auth.role == 'STUDENT' or 'REGISTRAR'`
**VALIDATE:**

* CHECK: auth.role in ['STUDENT','REGISTRAR']

  * FAIL_WITH: 403, `ERR_UNAUTHORIZED_ROLE`
* CHECK: (auth.role == 'STUDENT' and auth.id == path.studentId) or auth.role == 'REGISTRAR'

  * FAIL_WITH: 403, `ERR_FORBIDDEN` (Students can only pay their own account)
* CHECK: @AcademicTerm[path.termId].exists()

  * FAIL_WITH: 404, `ERR_TERM_NOT_FOUND`
* CHECK: @User[path.studentId].role == 'STUDENT'

  * FAIL_WITH: 404, `ERR_STUDENT_NOT_FOUND`
* CHECK: body.amount != null && body.amount > 0

  * FAIL_WITH: 422, `ERR_INVALID_PAYMENT_AMOUNT`
* CHECK: @Ledger:StudentTuitionLedger.exists()

  * (If not exists, init ledger with 0 for that student first – assume ledger.init here if needed)
* CHECK: body.amount <= @Ledger:StudentTuitionLedger.balance_cents

  * FAIL_WITH: 422, `ERR_OVERPAY_NOT_ALLOWED`
    **ACTIONS:**
* DO: ledger.debit(@Ledger:StudentTuitionLedger, 'balance_cents', body.amount)
* DO: audit.log('tuition_paid', { term: path.termId, student: path.studentId, amount: body.amount, by: auth.id })
* DO: if(@Ledger:StudentTuitionLedger.balance_cents == 0) {
  audit.log('tuition_fully_paid', { term: path.termId, student: path.studentId });
  }
* DO: return { student_id: path.studentId, term_id: path.termId, new_balance: @Ledger:StudentTuitionLedger.balance_cents }

*(This is a straightforward financial operation with validation. It doesn’t trigger additional state changes, but ensures ledger invariants.)*

---

**Derivations:** Several fields are derived dynamically from live data or other resources:

* **Course.enrolled_count** – *Derived:* Count of enrollments where `course_id == self.id` and `state == 'ENROLLED'`. (Updates whenever enrollments change.)
* **Course.waitlist_count** – *Derived:* Count of enrollments where `course_id == self.id` and `state == 'WAITLISTED'`.
* **Course.available_seats** – *Derived:* Equivalent to `Course.capacity - Course.enrolled_count`. In implementation, this is synced with `CourseSeatLedger.seats_available` for consistency. Essentially, `available_seats = @Ledger:CourseSeatLedger[self.term_id, self.id].seats_available`.
* **StudentTuitionLedger.balance_cents** – This ledger’s value is itself a derived aggregation of all the student’s course enrollments costs minus payments. It increases by `course.credits * COST_PER_CREDIT` for each enrollment (when moving to ENROLLED) and decreases on drops or payments. (No separate formula needed beyond ledger operations defined.)
* **Enrollment.grade** – *Contextual Visibility:* This field is only populated once `Enrollment.state == 'COMPLETED'` and even then, only visible to the enrollment’s student, the course’s professor, and the Registrar. If present, it’s set during some grade assignment process (not detailed in this spec). Before completion, or to unauthorized viewers, `grade` will be `null` or omitted.
* **User.is_full_time (contextual)** – Though `User` is not a resource here, we can conceptually derive whether a student is full-time in a term by checking if their total enrolled credits >= FULL_TIME_CREDITS_THRESHOLD (12). If we were to include a field in a GET Term or profile response for a student, it would use: `is_full_time = (Σ credits of ENROLLED courses in term >= 12)`.

**Hierarchy of Rules:** In cases where multiple rules could apply at once, the system’s evaluation order ensures container-level and global constraints are checked first, followed by more granular checks. For example, an attempt by a student to enroll after registration is closed will be blocked by the term state (`ERR_REGISTRATION_CLOSED`) before even checking course capacity or credit limits. Likewise, if a professor tries to publish a course in a term that isn’t open, the term state rule (`ERR_TERM_NOT_ACTIVE`) takes precedence over, say, any other checks. This hierarchy is important to avoid conflicting outcomes – e.g., **Term closure > Course state > Role permissions > Ledger limits** in priority. In our design, we’ve structured validations such that broader scope conditions are checked earlier in each rule, effectively implementing this precedence. Where an override exists (Registrar bypassing credit limit), the rules explicitly check the role and skip the normal failure. This approach ensures a clear "source of truth": container locks (like term state or course cancelled) override any user-level intents, and within allowed operations, resource-specific rules (like capacity or credit limit) then apply unless overridden by an authorized role as specified.